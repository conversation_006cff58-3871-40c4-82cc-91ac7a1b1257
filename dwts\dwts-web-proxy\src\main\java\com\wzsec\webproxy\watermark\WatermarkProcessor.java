package com.wzsec.webproxy.watermark;

import com.wzsec.webproxy.domain.WebProxyConfig;

import javax.servlet.http.HttpServletRequest;

/**
 * 水印处理器接口
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
public interface WatermarkProcessor {

    /**
     * 处理水印注入
     *
     * @param content     原始内容
     * @param contentType 内容类型
     * @param request     HTTP请求
     * @param config      代理配置
     * @return 处理后的内容
     */
    byte[] processWatermark(byte[] content, String contentType, 
                           HttpServletRequest request, WebProxyConfig config);

    /**
     * 检查是否可以处理指定的内容类型
     *
     * @param contentType 内容类型
     * @return 是否可以处理
     */
    boolean canHandle(String contentType);

    /**
     * 获取处理器名称
     *
     * @return 处理器名称
     */
    String getProcessorName();

    /**
     * 获取水印类型
     *
     * @return 水印类型
     */
    String getWatermarkType();
}
