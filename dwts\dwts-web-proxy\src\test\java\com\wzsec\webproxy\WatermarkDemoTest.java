package com.wzsec.webproxy;

import com.wzsec.webproxy.util.WatermarkPrinter;
import org.junit.jupiter.api.Test;

/**
 * 水印演示测试
 * 用于展示水印前后的文字对比效果
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
public class WatermarkDemoTest {

    @Test
    public void testJsonWatermark() {
        System.out.println("🧪 测试JSON内容暗水印效果");
        
        String jsonContent = "{\n" +
                "  \"code\": 200,\n" +
                "  \"message\": \"success\",\n" +
                "  \"data\": {\n" +
                "    \"users\": [\n" +
                "      {\n" +
                "        \"id\": 1,\n" +
                "        \"name\": \"张三\",\n" +
                "        \"email\": \"<EMAIL>\",\n" +
                "        \"department\": \"技术部\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"id\": 2,\n" +
                "        \"name\": \"李四\",\n" +
                "        \"email\": \"<EMAIL>\",\n" +
                "        \"department\": \"市场部\"\n" +
                "      }\n" +
                "    ],\n" +
                "    \"total\": 2\n" +
                "  }\n" +
                "}";

        WatermarkPrinter.printWatermarkComparison(jsonContent, "application/json");
    }

    @Test
    public void testTextWatermark() {
        System.out.println("🧪 测试纯文本暗水印效果");
        
        String textContent = "这是一份重要的商业机密文档。\n" +
                "包含了公司的核心技术资料和客户信息。\n" +
                "请妥善保管，不得外泄。\n" +
                "如有疑问，请联系信息安全部门。";

        WatermarkPrinter.printWatermarkComparison(textContent, "text/plain");
    }

    @Test
    public void testXmlWatermark() {
        System.out.println("🧪 测试XML内容暗水印效果");
        
        String xmlContent = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<response>\n" +
                "  <status>success</status>\n" +
                "  <data>\n" +
                "    <user id=\"1\">\n" +
                "      <name>张三</name>\n" +
                "      <email><EMAIL></email>\n" +
                "    </user>\n" +
                "    <user id=\"2\">\n" +
                "      <name>李四</name>\n" +
                "      <email><EMAIL></email>\n" +
                "    </user>\n" +
                "  </data>\n" +
                "</response>";

        WatermarkPrinter.printWatermarkComparison(xmlContent, "application/xml");
    }

    @Test
    public void testSimpleTextWatermark() {
        System.out.println("🧪 测试简单文本暗水印效果");
        
        String simpleText = "Hello World! 这是一个简单的测试文本。";

        WatermarkPrinter.printWatermarkComparison(simpleText, "text/plain");
    }

    @Test
    public void demonstrateZeroWidthCharacters() {
        System.out.println("\n" + "=".repeat(80));
        System.out.println("🔍 零宽字符说明");
        System.out.println("=".repeat(80));
        
        System.out.println("\n📚 零宽字符类型:");
        System.out.println("   • ZERO_WIDTH_SPACE (\\u200B)      - 零宽空格");
        System.out.println("   • ZERO_WIDTH_NON_JOINER (\\u200C) - 零宽非连接符");
        System.out.println("   • ZERO_WIDTH_JOINER (\\u200D)     - 零宽连接符");
        System.out.println("   • WORD_JOINER (\\u2060)           - 词连接符");
        System.out.println("   • INVISIBLE_SEPARATOR (\\u2062)   - 不可见分隔符");
        
        System.out.println("\n🔒 水印编码原理:");
        System.out.println("   1. 将用户信息编码为字符串: user123|192.168.1.100|1704067200000|sess_abc|chk1");
        System.out.println("   2. 将字符串转换为二进制");
        System.out.println("   3. 每2位二进制映射为一个零宽字符");
        System.out.println("   4. 在文本的合适位置插入这些零宽字符");
        System.out.println("   5. 用户看到的内容完全正常，但包含了不可见的水印");
        
        System.out.println("\n✨ 特点:");
        System.out.println("   • 完全不可见 - 用户无法察觉");
        System.out.println("   • 复制保留 - 复制粘贴时水印会一起被复制");
        System.out.println("   • 跨平台 - 在任何支持Unicode的系统中都有效");
        System.out.println("   • 防篡改 - 包含校验和，确保完整性");
    }
}
