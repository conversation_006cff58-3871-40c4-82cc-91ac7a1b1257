package com.wzsec.webproxy.handler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;

/**
 * 通用代理处理器
 * 提供通用的代理处理功能，支持所有网站的子域名代理
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Slf4j
@Component
public class UniversalProxyHandler {

    /**
     * 为任意网站请求创建合适的请求头
     */
    public HttpHeaders createProxyHeaders(HttpServletRequest request, String targetHost) {
        HttpHeaders headers = new HttpHeaders();

        // 复制原始请求头，但跳过一些特殊头部
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();

            if (shouldSkipHeader(headerName)) {
                continue;
            }

            Enumeration<String> headerValues = request.getHeaders(headerName);
            while (headerValues.hasMoreElements()) {
                headers.add(headerName, headerValues.nextElement());
            }
        }

        // 设置目标主机的Host头
        headers.set("Host", targetHost);

        // 设置通用的浏览器请求头
        if (!headers.containsKey("User-Agent")) {
            headers.set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
        }

        if (!headers.containsKey("Accept")) {
            headers.set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7");
        }

        if (!headers.containsKey("Accept-Language")) {
            headers.set("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
        }

        if (!headers.containsKey("Accept-Encoding")) {
            headers.set("Accept-Encoding", "gzip, deflate, br");
        }

        // 移除可能导致问题的代理相关头部
        headers.remove("X-Forwarded-For");
        headers.remove("X-Forwarded-Proto");
        headers.remove("X-Forwarded-Host");
        headers.remove("X-Real-IP");

        return headers;
    }


    /**
     * 提取基础域名（去掉子域名）
     */
    public String extractBaseDomain(String host) {
        if (host == null) return null;

        String[] parts = host.split("\\.");
        if (parts.length >= 2) {
            // 返回最后两部分作为基础域名，如 baidu.com, google.com
            return parts[parts.length - 2] + "." + parts[parts.length - 1];
        }
        return host;
    }

    /**
     * 检查是否应该跳过某个请求头
     */
    private boolean shouldSkipHeader(String headerName) {
        String lowerName = headerName.toLowerCase();
        return lowerName.equals("host") ||
                lowerName.equals("content-length") ||
                lowerName.startsWith("x-forwarded-") ||
                lowerName.equals("connection") ||
                lowerName.equals("upgrade") ||
                lowerName.equals("proxy-connection") ||
                lowerName.equals("transfer-encoding") ||
                lowerName.equals("te") ||
                lowerName.startsWith("sec-") ||
                lowerName.equals("origin") ||
                lowerName.equals("referer");
    }

}
