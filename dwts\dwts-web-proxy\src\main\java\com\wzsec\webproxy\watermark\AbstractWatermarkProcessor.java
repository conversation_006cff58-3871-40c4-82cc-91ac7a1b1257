package com.wzsec.webproxy.watermark;

import com.wzsec.webproxy.domain.WebProxyConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;

/**
 * 水印处理器抽象基类
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Slf4j
public abstract class AbstractWatermarkProcessor implements WatermarkProcessor {

    /**
     * 生成水印文本
     *
     * @param request HTTP请求
     * @param config  代理配置
     * @return 水印文本
     */
    protected String generateWatermarkText(HttpServletRequest request, WebProxyConfig config) {
        String watermarkText = config.getWatermarkText();
        
        if (StringUtils.isBlank(watermarkText)) {
            watermarkText = "DWTS水印";
        }
        
        // 替换变量
        String clientIp = getClientIpAddress(request);
        String currentTime = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
        String currentDate = new SimpleDateFormat("yyyyMMdd").format(new Date());
        
        watermarkText = watermarkText.replace("{IP}", clientIp)
                                   .replace("{TIME}", currentTime)
                                   .replace("{DATE}", currentDate)
                                   .replace("{USER}", getCurrentUser(request));
        
        return watermarkText;
    }

    /**
     * 获取客户端IP地址
     *
     * @param request HTTP请求
     * @return 客户端IP
     */
    protected String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (StringUtils.isNotBlank(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (StringUtils.isNotBlank(xRealIp)) {
            return xRealIp;
        }
        
        String remoteAddr = request.getRemoteAddr();
        if ("127.0.0.1".equals(remoteAddr) || "0:0:0:0:0:0:0:1".equals(remoteAddr)) {
            try {
                remoteAddr = InetAddress.getLocalHost().getHostAddress();
            } catch (UnknownHostException e) {
                log.warn("获取本机IP失败", e);
            }
        }
        
        return remoteAddr;
    }

    /**
     * 获取当前用户（可以从session或token中获取）
     *
     * @param request HTTP请求
     * @return 用户标识
     */
    protected String getCurrentUser(HttpServletRequest request) {
        // 可以从session中获取用户信息
        String user = (String) request.getSession().getAttribute("username");
        if (StringUtils.isNotBlank(user)) {
            return user;
        }
        
        // 或者从请求头中获取
        String authorization = request.getHeader("Authorization");
        if (StringUtils.isNotBlank(authorization)) {
            // 这里可以解析JWT token获取用户信息
            return "USER";
        }
        
        return "ANONYMOUS";
    }

    /**
     * 生成Base64编码的SVG水印
     *
     * @param text   水印文本
     * @param config 代理配置
     * @return Base64编码的SVG
     */
    protected String generateWatermarkSvg(String text, WebProxyConfig config) {
        int width = config.getWatermarkWidth() != null ? config.getWatermarkWidth() : 300;
        int height = config.getWatermarkHeight() != null ? config.getWatermarkHeight() : 150;
        String color = config.getWatermarkColor() != null ? config.getWatermarkColor() : "#666666";
        double angle = config.getWatermarkAngle() != null ? config.getWatermarkAngle() : -30.0;
        
        int fontSize = Math.min(width / text.length() * 2, height / 3);
        if (fontSize < 12) fontSize = 12;
        if (fontSize > 24) fontSize = 24;
        
        String svg = String.format(
            "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" +
            "<svg width=\"%d\" height=\"%d\" xmlns=\"http://www.w3.org/2000/svg\">" +
            "<text x=\"%d\" y=\"%d\" font-size=\"%d\" fill=\"%s\" font-family=\"Arial, sans-serif\" " +
            "transform=\"rotate(%.1f %d,%d)\">%s</text>" +
            "</svg>",
            width, height,
            width / 2, height / 2,
            fontSize, color,
            angle, width / 2, height / 2,
            escapeXml(text)
        );
        
        return Base64.getEncoder().encodeToString(svg.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * XML转义
     *
     * @param text 原始文本
     * @return 转义后的文本
     */
    protected String escapeXml(String text) {
        if (text == null) {
            return "";
        }
        return text.replace("&", "&amp;")
                  .replace("<", "&lt;")
                  .replace(">", "&gt;")
                  .replace("\"", "&quot;")
                  .replace("'", "&#39;");
    }

    /**
     * HTML转义
     *
     * @param text 原始文本
     * @return 转义后的文本
     */
    protected String escapeHtml(String text) {
        if (text == null) {
            return "";
        }
        return text.replace("&", "&amp;")
                  .replace("<", "&lt;")
                  .replace(">", "&gt;")
                  .replace("\"", "&quot;")
                  .replace("'", "&#x27;")
                  .replace("/", "&#x2F;");
    }

    /**
     * JavaScript转义
     *
     * @param text 原始文本
     * @return 转义后的文本
     */
    protected String escapeJavaScript(String text) {
        if (text == null) {
            return "";
        }
        return text.replace("\\", "\\\\")
                  .replace("\"", "\\\"")
                  .replace("'", "\\'")
                  .replace("\n", "\\n")
                  .replace("\r", "\\r")
                  .replace("\t", "\\t");
    }

    /**
     * 检查内容类型是否匹配
     *
     * @param contentType    实际内容类型
     * @param expectedTypes  期望的内容类型列表
     * @return 是否匹配
     */
    protected boolean isContentTypeMatch(String contentType, String... expectedTypes) {
        if (contentType == null) {
            return false;
        }
        
        String lowerContentType = contentType.toLowerCase();
        for (String expectedType : expectedTypes) {
            if (lowerContentType.contains(expectedType.toLowerCase())) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 安全地转换字节数组为字符串
     *
     * @param content 字节数组
     * @return 字符串
     */
    protected String safeToString(byte[] content) {
        if (content == null || content.length == 0) {
            return "";
        }
        return new String(content, StandardCharsets.UTF_8);
    }

    /**
     * 安全地转换字符串为字节数组
     *
     * @param content 字符串
     * @return 字节数组
     */
    protected byte[] safeToBytes(String content) {
        if (content == null) {
            return new byte[0];
        }
        return content.getBytes(StandardCharsets.UTF_8);
    }
}
