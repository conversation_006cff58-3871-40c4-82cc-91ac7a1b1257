# API 暗水印系统操作指南

## 📋 目录
1. [系统配置](#系统配置)
2. [基础操作](#基础操作)
3. [高级功能](#高级功能)
4. [溯源分析](#溯源分析)
5. [监控告警](#监控告警)
6. [故障排查](#故障排查)

## 🔧 系统配置

### 1. 数据库初始化

```sql
-- 执行数据库迁移脚本
source /path/to/dwts-web-proxy/src/main/resources/db/migration/V1.1__Add_Invisible_Watermark_Fields.sql
```

### 2. 应用配置

在 `application.yml` 中配置暗水印参数：

```yaml
web-proxy:
  watermark:
    invisible:
      enabled: true
      encoding-strength: medium  # low, medium, high
      embed-density: 0.3  # 0.1-1.0
      max-embed-length: 1000
```

### 3. 启动应用

```bash
# 启动 dwts-web-proxy 服务
cd dwts/dwts-web-proxy
mvn spring-boot:run
```

## 🚀 基础操作

### 1. 查看当前配置

```bash
# 获取所有水印配置
curl -X GET "http://localhost:9090/api/watermark/config/list" \
  -H "Content-Type: application/json"

# 获取指定代理的配置
curl -X GET "http://localhost:9090/api/watermark/config/proxy/my-proxy" \
  -H "Content-Type: application/json"
```

### 2. 启用暗水印功能

```bash
# 更新指定配置的暗水印设置
curl -X PUT "http://localhost:9090/api/watermark/config/invisible/1" \
  -H "Content-Type: application/json" \
  -d '{
    "enableInvisible": true,
    "encodingStrength": "medium",
    "embedDensity": 0.5
  }'
```

### 3. 批量操作

```bash
# 批量启用暗水印
curl -X PUT "http://localhost:9090/api/watermark/config/invisible/batch" \
  -H "Content-Type: application/json" \
  -d '{
    "configIds": [1, 2, 3],
    "enabled": true
  }'
```

### 4. 验证配置

```bash
# 验证配置参数
curl -X POST "http://localhost:9090/api/watermark/config/invisible/validate" \
  -H "Content-Type: application/json" \
  -d '{
    "encodingStrength": "high",
    "embedDensity": 0.8
  }'
```

## 🎯 高级功能

### 1. 获取推荐配置

```bash
# 获取不同安全级别的推荐配置
curl -X GET "http://localhost:9090/api/watermark/config/recommended?securityLevel=high" \
  -H "Content-Type: application/json"
```

### 2. 复制配置

```bash
# 将源配置复制到多个目标配置
curl -X POST "http://localhost:9090/api/watermark/config/copy" \
  -H "Content-Type: application/json" \
  -d '{
    "sourceConfigId": 1,
    "targetConfigIds": [2, 3, 4]
  }'
```

### 3. 重置配置

```bash
# 重置为默认配置
curl -X POST "http://localhost:9090/api/watermark/config/invisible/1/reset" \
  -H "Content-Type: application/json"
```

## 🔍 溯源分析

### 1. 分析可疑内容

```bash
# 分析包含水印的内容
curl -X POST "http://localhost:9090/api/watermark/trace/analyze" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "这里是可疑的泄露内容...",
    "contentType": "application/json"
  }'
```

### 2. 文件水印提取

```bash
# 从文件中提取水印
curl -X POST "http://localhost:9090/api/watermark/trace/extract-from-file" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@suspicious_file.json" \
  -F "contentType=application/json"
```

### 3. 快速检测

```bash
# 快速检测是否包含水印
curl -X POST "http://localhost:9090/api/watermark/trace/quick-detect" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "待检测的内容...",
    "contentType": "text/plain"
  }'
```

### 4. 水印完整性验证

```bash
# 验证水印完整性
curl -X POST "http://localhost:9090/api/watermark/trace/verify" \
  -H "Content-Type: application/json" \
  -d '{
    "originalContent": "原始内容...",
    "contentToVerify": "待验证内容...",
    "contentType": "application/json"
  }'
```

## 📊 监控告警

### 1. 获取统计信息

```bash
# 获取配置统计
curl -X GET "http://localhost:9090/api/watermark/config/statistics" \
  -H "Content-Type: application/json"

# 获取溯源统计
curl -X GET "http://localhost:9090/api/watermark/trace/statistics" \
  -H "Content-Type: application/json"
```

### 2. 设置监控脚本

创建监控脚本 `monitor_watermark.sh`：

```bash
#!/bin/bash

# 检查水印配置状态
check_config_status() {
    response=$(curl -s "http://localhost:9090/api/watermark/config/statistics")
    enabled_count=$(echo $response | jq '.data.invisibleWatermarkEnabled')
    total_count=$(echo $response | jq '.data.totalConfigs')
    
    echo "暗水印启用状态: $enabled_count/$total_count"
    
    if [ "$enabled_count" -lt "$total_count" ]; then
        echo "警告: 部分代理未启用暗水印功能"
    fi
}

# 检查溯源分析
check_trace_activity() {
    # 这里可以添加检查最近溯源活动的逻辑
    echo "检查溯源活动..."
}

# 执行检查
check_config_status
check_trace_activity
```

### 3. 设置定时任务

```bash
# 添加到 crontab
crontab -e

# 每小时检查一次
0 * * * * /path/to/monitor_watermark.sh >> /var/log/watermark_monitor.log 2>&1
```

## 🛠️ 故障排查

### 1. 常见问题

#### 问题1: 水印未生效
```bash
# 检查配置状态
curl -X GET "http://localhost:9090/api/watermark/config/proxy/your-proxy-name"

# 检查日志
tail -f logs/dwts-web-proxy.log | grep -i watermark
```

#### 问题2: 溯源分析失败
```bash
# 验证内容格式
curl -X POST "http://localhost:9090/api/watermark/trace/quick-detect" \
  -H "Content-Type: application/json" \
  -d '{"content": "test content", "contentType": "text/plain"}'
```

#### 问题3: 性能影响
```bash
# 调整编码强度
curl -X PUT "http://localhost:9090/api/watermark/config/invisible/1" \
  -H "Content-Type: application/json" \
  -d '{
    "enableInvisible": true,
    "encodingStrength": "low",
    "embedDensity": 0.2
  }'
```

### 2. 日志分析

```bash
# 查看水印处理日志
grep "InvisibleWatermarkProcessor" logs/dwts-web-proxy.log

# 查看溯源分析日志
grep "WatermarkTraceService" logs/dwts-web-proxy.log

# 查看配置变更日志
grep "WatermarkConfigService" logs/dwts-web-proxy.log
```

### 3. 性能调优

#### 调整编码参数
- **低性能影响**: `encodingStrength: "low"`, `embedDensity: 0.1-0.3`
- **平衡模式**: `encodingStrength: "medium"`, `embedDensity: 0.3-0.5`
- **高安全性**: `encodingStrength: "high"`, `embedDensity: 0.5-0.8`

#### 优化配置
```yaml
web-proxy:
  watermark:
    invisible:
      enabled: true
      encoding-strength: medium
      embed-density: 0.3
      max-embed-length: 500  # 减少最大嵌入长度
```

## 📝 最佳实践

### 1. 安全配置建议
- 生产环境建议使用 `medium` 或 `high` 编码强度
- 定期轮换水印文本模板
- 启用所有类型的水印（页面、API、暗水印）

### 2. 监控建议
- 设置自动化监控脚本
- 定期检查配置一致性
- 监控溯源分析结果

### 3. 应急响应
- 发现泄露时立即进行溯源分析
- 保存分析结果作为证据
- 根据风险等级采取相应措施

## 🔗 相关链接

- [API 文档](http://localhost:9090/swagger-ui.html)
- [系统架构文档](./ARCHITECTURE.md)
- [开发指南](./DEVELOPMENT_GUIDE.md)
