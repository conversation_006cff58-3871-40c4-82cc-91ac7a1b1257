package com.wzsec.utils.database;

import com.alibaba.fastjson.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.*;

/**
 * 创建Informix表并批量插入数据操作类
 */
public class InformixUtil extends DatabaseUtil {

    private final static Logger log = LoggerFactory.getLogger(InformixUtil.class);
    private static String JDBC_DRIVER = "com.informix.jdbc.IfxDriver";

    /**
     * @Description:获取数据库连接
     * <AUTHOR>
     * @date 2022-12-27
     */
    public static Connection getConn(String url, String username, String password) {
        Connection conn = null;
        try {
            conn = DatabaseUtil.getConn(JDBC_DRIVER, url, username, password);// 打开连接
        } catch (Exception ex) {
            System.out.println("获取数据库连接出现异常");
            throw ex;
            //log.error("获取数据库中所有的库名表名出现异常");
        }
        return conn;
    }

    /**
     * 通过Map数据集合生成批量插入SQL语句及插入语句参数
     *
     * @param objList   Map数据库集合
     * @param tableName 表名
     * @param start     开始
     * @param end       结束
     * @param dbname    dbname
     * @return Map<String, Object>：key为“sql”是批量插入语句，key为“params”是插入语句参数
     */
    @Override
    public Map<String, Object> getInsert2TableSqlAndPatams(int start, int end, List<Map<String, String>> objList, String dbname, String tableName, String fieldnames) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO ").append(tableName).append(" (");
            for (String column : fields) {
                sb.append("").append(column).append(", ");
            }
            String sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sb = new StringBuilder(sql);
            sb.append(") VALUES ");
            for (int i = start; i < end; i++) {
                Map<String, String> map = objList.get(i);
                sb.append("(");
                for (String key : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?, ");
                    params.add(map.get(key));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append("), ");
            }
            sql = sb.toString();
            lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            // sql += ";";
            sqlAndParams = new HashMap<>();
            sqlAndParams.put("sql", sql);
            sqlAndParams.put("params", params.toArray());
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    public static Map<String, Object> getInsertTableSqlAndPatams(int start, int end, List<Map<String, String>> objList, String dbname, String tableName) {
        Map<String, Object> sqlAndParams = null;
        List<String> sqlList = new ArrayList<>();
        List<List<Object>> paramsList = new ArrayList<>();
        try {
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO ").append(tableName).append(" (");
            for (String column : fields) {
                sb.append(column).append(", ");
            }
            String sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sb = new StringBuilder(sql);
            sb.append(") VALUES ");
            String insertFieldSql = sb.toString();

            for (int i = start; i < end; i++) {
                List<Object> parList = new ArrayList<>();
                StringBuilder paramsStr = new StringBuilder();
                Map<String, String> map = objList.get(i);
                paramsStr.append("(");
                for (String key : fields) {// 循环字段名，使用fields保证顺序一致
                    paramsStr.append("?,");
                    parList.add(map.get(key));
                }
                int lastParamsIndex = paramsStr.lastIndexOf(",");// 去掉最后一个逗号
                String interSql = insertFieldSql + paramsStr.substring(0,lastParamsIndex) + ")";
                sqlList.add(interSql);
                paramsList.add(parList);
            }
            sqlAndParams = new HashMap<>();
            sqlAndParams.put("sql",sqlList);
            sqlAndParams.put("params",paramsList);
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    /**
     * 查询表字段信息
     *
     * @param tableName 表名
     * @param dburl     数据库连接信息
     * @param username  数据库用户名
     * @param password  数据库密码
     * @return List<Map < String, String>>：数据库字段信息
     */
    @Override
    protected List<Map<String, String>> getTableFieldInfo(String dbName, String tableName, String dburl, String username, String password) {
        List<Map<String, String>> fieldInfoList = null;
        Connection conn = null;
        Statement stmt = null;
        String sql = "";
        try {
            conn = getConn(dburl, username, password);// 打开连接
            stmt = conn.createStatement();// 执行创建表
            sql = "select c.colname,c.collength,\n" +
                    "case c.coltype \n" +
                    "when '0' then 'CHAR'\n" +
                    "when '1' then 'SMALLINT'\n" +
                    "when '2' then 'INTEGER'\n" +
                    "when '3' then 'FLOAT'\n" +
                    "when '4' then 'SMALLFLOAT'\n" +
                    "when '5' then 'DECIMAL'\n" +
                    "when '6' then 'SERIAL'\n" +
                    "when '7' then 'DATE'\n" +
                    "when '8' then 'MONEY'\n" +
                    "when '9' then 'NULL'\n" +
                    "when '10' then 'DATETIME'\n" +
                    "when '11' then 'BYTE'\n" +
                    "when '12' then 'TEXT'\n" +
                    "when '13' then 'VARCHAR'\n" +
                    "when '14' then 'INTERVAL'\n" +
                    "when '15' then 'NCHAR'\n" +
                    "when '16' then 'NVARCHAR'\n" +
                    "when '17' then 'INT8'\n" +
                    "when '18' then 'SERIAL8'\n" +
                    "when '19' then 'SET'\n" +
                    "when '20' then 'MULTISET'\n" +
                    "when '21' then 'LIST'\n" +
                    "when '22' then 'Unnamed ROW'\n" +
                    "when '40' then 'LVARCHAR'\n" +
                    "when '41' then 'CLOB'\n" +
                    "when '43' then 'BLOB'\n" +
                    "when '44' then 'BOOLEAN'\n" +
                    "when '256' then 'CHAR'\n" +
                    "when '257' then 'SMALLINT'\n" +
                    "when '258' then 'INTEGER'\n" +
                    "when '259' then 'FLOAT'\n" +
                    "when '260' then 'REAL'\n" +
                    "when '261' then 'DECIMAL'\n" +
                    "when '262' then 'SERIAL'\n" +
                    "when '263' then 'DATE'\n" +
                    "when '264' then 'MONEY'\n" +
                    "when '266' then 'DATETIME'\n" +
                    "when '267' then 'BYTE'\n" +
                    "when '268' then 'TEXT'\n" +
                    "when '269' then 'VARCHAR'\n" +
                    "when '270' then 'INTERVAL'\n" +
                    "when '271' then 'NCHAR'\n" +
                    "when '272' then 'NVARCHAR'\n" +
                    "when '273' then 'INT8'\n" +
                    "when '274' then 'SERIAL8'\n" +
                    "when '275' then 'SET'\n" +
                    "when '276' then 'MULTISET'\n" +
                    "when '277' then 'LIST'\n" +
                    "when '278' then 'Unnamed ROW'\n" +
                    "when '296' then 'LVARCHAR'\n" +
                    "when '297' then 'CLOB'\n" +
                    "when '298' then 'BLOB'\n" +
                    "when '299' then 'BOOLEAN'\n" +
                    "when '4118' then 'Named ROW'\n" +
                    "end as coltypename from informix.systables as t, informix.syscolumns as c \n" +
                    "where t.tabname = '" + tableName + "' and t.tabid = c.tabid";
            ResultSet rs = stmt.executeQuery(sql);
            if (rs != null) {
                fieldInfoList = new ArrayList<>();
                while (rs.next()) {
                    Map<String, String> fieldInfoMap = new HashMap<>();
                    //TODO 所有DBUtil 字段信息尽量统一，以便支持不同数据源类型之间脱敏
                    fieldInfoMap.put("FieldEName", rs.getString("colname"));//字段名
                    fieldInfoMap.put("FieldCName", null);

                    //通过数据长度推算出decimal类型的数据精度
                    String colTypeName = rs.getString("coltypename");
                    log.info("colTypeName:" + colTypeName);
                    String FieldType = null;
                    if (colTypeName.contains("DECIMAL")) {
                        Integer colLength = rs.getInt("collength");
                        Integer m = (colLength >> 8);
                        Integer n = (colLength & 255);
                        FieldType = colTypeName + "(" + m + "," + n + ")";
                    } else if (colTypeName.contains("INT") || colTypeName.contains("TEXT") ||
                            colTypeName.contains("BOOL") || colTypeName.contains("DATE")) {
                        FieldType = colTypeName;
                    } else {
                        FieldType = colTypeName + "(" + rs.getString("collength") + ")";
                    }
                    fieldInfoMap.put("FieldType", FieldType);//格式：varchar(255)
                    fieldInfoList.add(fieldInfoMap);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        closeCon(null, stmt, conn);

        log.info("informix字段信息：" + JSON.toJSONString(fieldInfoList));
        return fieldInfoList;
    }

    @Override
    protected Map<String, Object> getInsert2TableSqlAndPatams(List<Map<String, String>> objList, String dbName, String tableName) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO ").append(tableName).append(" (");
            for (String column : fields) {
                sb.append("").append(column).append(", ");
            }
            String sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sb = new StringBuilder(sql);
            sb.append(") VALUES ");
            for (int i = 0; i < objList.size(); i++) {
                Map<String, String> map = objList.get(i);
                sb.append("(");
                for (String key : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?, ");
                    params.add(map.get(key));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append("), ");
            }
            sql = sb.toString();
            lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            // sql += ";";
            sqlAndParams = new HashMap<>();
            sqlAndParams.put("sql", sql);
            sqlAndParams.put("params", params.toArray());
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    /**
     * 通过Map生成创建表SQL语句，自动检测字段名及类型
     *
     * @param obj           Map对象
     * @param tableName     表名
     * @param fieldInfoList 字段信息列表
     * @param maskfields    maskfields
     */
    @Override
    protected String getCreateTableSql(List<Map<String, String>> fieldInfoList, Map<String, String> obj, String tableName, List<String> maskfields) {
        String sql = null;
        try {
            StringBuilder sb = new StringBuilder();
            // sb.append("\r\nDROP TABLE IF EXISTS
            // ").append("`").append(tableName).append("`").append(";\r\n");//删除表语句
            sb.append("CREATE TABLE ").append(tableName).append(" (\r\n");
            // boolean firstId = true;
            for (Map<String, String> fieldInfo : fieldInfoList) {
                if (!obj.keySet().contains(fieldInfo.get("Field"))) {// 跳过没有抽取的列
                    continue;
                }
                sb.append(fieldInfo.get("Field"));// 字段名
                if (maskfields != null && maskfields.contains(fieldInfo.get("Field"))) {// 脱敏的字段类型更改为varchar
                    sb.append(" varchar(255)");// 类型
                } else {
                    sb.append(" ").append(fieldInfo.get("Type"));// 类型
                }
                if ("NO".equalsIgnoreCase(fieldInfo.get("Null"))) {// 判断非空
                    sb.append(" NOT NULL");
                }
                if ("auto_increment".equalsIgnoreCase(fieldInfo.get("Extra"))) {// 判断非空
                    sb.append(" AUTO_INCREMENT");// 自增
                } else {
                    if (fieldInfo.get("Default") != null) {
                        sb.append(" DEFAULT '").append(fieldInfo.get("Default")).append("'");// 默认值
                    } else {
                        sb.append(" DEFAULT NULL");
                    }
                }
                if ("PRI".equalsIgnoreCase(fieldInfo.get("Key"))) {
                    sb.append(" PRIMARY KEY");// 主键
                }
                if (fieldInfo.get("Comment") != null && !"".equals(fieldInfo.get("Comment"))) {
                    sb.append(" COMMENT '").append(fieldInfo.get("Comment")).append("'");
                }
                sb.append(",\n");
            }
            sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sql = sql + "\r);";
        } catch (Exception e) {
            e.printStackTrace();
            sql = null;
        }
        return sql;
    }

    /**
     * 通过Map数据集合生成批量插入SQL语句及插入语句参数
     *
     * @param objList   Map数据库集合
     * @param tableName 表名
     * @param start     开始
     * @param end       结束
     * @param dbname    dbname
     * @return Map<String, Object>：key为“sql”是批量插入语句，key为“params”是插入语句参数
     */
    @Override
    public Map<String, Object> getInsert2TableSqlAndPatams(int start, int end, List<Map<String, String>> objList, String dbname, String tableName) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO ").append(tableName).append(" (");
            for (String column : fields) {
                sb.append("").append(column).append(", ");
            }
            String sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sb = new StringBuilder(sql);
            sb.append(") VALUES ");
            for (int i = start; i < end; i++) {
                Map<String, String> map = objList.get(i);
                sb.append("(");
                for (String key : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?, ");
                    params.add(map.get(key));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append("), ");
            }
            sql = sb.toString();
            lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            // sql += ";";
            sqlAndParams = new HashMap<>();
            sqlAndParams.put("sql", sql);
            sqlAndParams.put("params", params.toArray());
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    /**
     * 通过Map生成创建表SQL语句，自动检测字段名及类型
     *
     * @param obj           Map对象
     * @param tableName     表名
     * @param fieldInfoList 字段信息列表
     * @param maskfields    maskfields
     * @param dbname        dbname
     */
    @Override
    protected String getCreateTableSql(List<Map<String, String>> fieldInfoList, Map<String, String> obj, String tableName, List<String> maskfields, String dbname, String watermarkField) {
        String sql = null;
        try {
            StringBuilder sb = new StringBuilder();
            // sb.append("\r\nDROP TABLE IF EXISTS
            // ").append("`").append(tableName).append("`").append(";\r\n");//删除表语句
            sb.append("CREATE TABLE ").append(tableName).append(" (\r\n");
            // boolean firstId = true;
            for (Map<String, String> fieldInfo : fieldInfoList) {
                if (!obj.keySet().contains(fieldInfo.get("FieldEName"))) {// 跳过没有抽取的列
                    continue;
                }
                sb.append(fieldInfo.get("FieldEName"));// 字段名
                String fieldType = " " + fieldInfo.get("FieldType");
                if (maskfields != null && maskfields.contains(fieldInfo.get("FieldEName"))) {// 脱敏的字段类型更改为varchar
                    sb.append(" VARCHAR(255)");// 类型
                } else {
//                    String colTypeName = fieldInfo.get("coltypename");
//                    if (colTypeName.trim().equalsIgnoreCase("VARCHAR")){
//                        sb.append(" ").append(colTypeName).append("(").append(fieldInfo.get("collength")).append(")");// 类型
//                    } else {
//                        sb.append(" ").append(colTypeName);// 类型
//                    }
                    if (fieldType.toLowerCase().contains("date")){
                        sb.append(" DATE ");
                    } else if (fieldType.toLowerCase().contains("integer")){
                        sb.append(" INTEGER ");
                    } else {
                        sb.append(fieldType);// 类型
                        sb.append(" DEFAULT NULL");
                    }
                }
//                if ("NO".equalsIgnoreCase(fieldInfo.get("Null"))) {// 判断非空
//                    sb.append(" NOT NULL");
//                }
//                if ("auto_increment".equalsIgnoreCase(fieldInfo.get("Extra"))) {// 判断非空
//                    sb.append(" AUTO_INCREMENT");// 自增

//                } else {
//                    if (fieldInfo.get("Default") != null) {
//                        sb.append(" DEFAULT '").append(fieldInfo.get("Default")).append("'");// 默认值
//                    } else {
//                        sb.append(" DEFAULT NULL");
//                    }
//                }
//                if ("PRI".equalsIgnoreCase(fieldInfo.get("Key"))) {
//                    sb.append(" PRIMARY KEY");// 主键
//                }
//                if (fieldInfo.get("Comment") != null && !"".equals(fieldInfo.get("Comment"))) {
//                    sb.append(" COMMENT '").append(fieldInfo.get("Comment")).append("'");
//                }
                sb.append(",\n");
            }
            sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            //sql = sql + "\r)ENGINE=InnoDB DEFAULT CHARSET= utf8;\r\n";
            sql = sql + "\r)\r\n";
        } catch (Exception e) {
            e.printStackTrace();
            sql = null;
        }
        return sql;
    }

    /**
     * @Description 数据库增删改方法
     * <AUTHOR>
     * @date 2019年10月15日10:07:46
     * @param sqlList：批量插入SQL语句
     * @param jdbcName：数据库驱动
     * @param dburl：数据库连接信息
     * @param username：数据库用户名
     * @param password：数据库密码
     * @return Boolean：true成功，false失败
     */
    public static Boolean executeUpdate(List<String> sqlList, List<List<Object>> paramsList, String jdbcName, String dburl,
                                        String username, String password) {
        Boolean result = true;
        Connection conn = null;
        PreparedStatement pstmt = null;
        String sqlStr = null;
        List<Object> params = null;
        try {
            conn = getConn(jdbcName, dburl, username, password);// 打开连接
            for (int i = 0; i < sqlList.size(); i++) {
                sqlStr = sqlList.get(i);
                pstmt = conn.prepareStatement(sqlStr);
                params = paramsList.get(i);
                for (int j = 0; j < params.size(); j++) {
                    pstmt.setObject(j + 1, params.get(j));
                }
                if (pstmt.executeUpdate() <= 0) {
                    throw new Exception("执行失败");
                }
            }
        } catch (Exception e) {
            log.info("异常执行sql:"+ sqlStr+",参数："+ JSON.toJSONString(params));
            e.printStackTrace();
            result = false;
        } finally {
            closeCon(null, pstmt, conn);
        }
        return result;
    }


    /**
     * 查询表信息
     * @param dbName
     * @param tableName
     * @param conn
     * @return
     */
    public static Map<String, String> getTableInfoBySchema(String dbName, String tableName, Connection conn) {
        Map<String, String> tableInfoMap = new HashMap<>();
        Statement stmt = null;
        ResultSet rs = null;
        try {
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery("SELECT TABLE_NAME,TABLE_COMMENT,TABLE_ROWS,ROUND(((data_length + index_length) / 1024 / 1024), 2) AS Size_MB FROM information_schema.TABLES WHERE TABLE_SCHEMA='" + dbName + "' and TABLE_NAME='" + tableName + "'");
            if (rs != null && rs.next()) {
                tableInfoMap.put("tableName", rs.getString("TABLE_NAME"));
                tableInfoMap.put("tableCName", rs.getString("TABLE_COMMENT"));
                tableInfoMap.put("tableRows", rs.getString("TABLE_ROWS"));
                tableInfoMap.put("dataSize", rs.getString("Size_MB"));
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs,stmt,null);
        }
        return tableInfoMap;
    }

}
