package com.wzsec.utils.database;

import lombok.extern.slf4j.Slf4j;

import java.sql.*;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 创建postgresql表并批量插入数据操作类
 *
 * <AUTHOR>
 * @Description 通过java List<Map<String,
 * Object>>生成SQL语句，批量插入语句，用于创建表，并批量插入表数据库操作类
 * @date 2019年10月11日 上午10:46:44
 */
@Slf4j
public class PostGreSQLUtil extends DatabaseUtil {

    private static String JDBC_DRIVER = "org.postgresql.Driver";

    /**
     * @Description:获取数据库连接
     * <AUTHOR>
     * @date 2020-4-22
     */
    public static Connection getConn(String url, String username, String password, String dbName) {
        Connection conn = null;
        try {
            conn = DatabaseUtil.getConn(JDBC_DRIVER, url, username, password);// 打开连接
        } catch (Exception ex) {
            System.out.println("获取数据库连接出现异常");
            throw ex;
            //log.error("获取数据库中所有的库名表名出现异常");
        }
        return conn;
    }


    /**
     * 获取库表(Map<库名,(表1,表2)>)
     *
     * @param conn    连接
     * @param dbnames dbname
     * @return {@link Map}<{@link String}, {@link String}>
     * @throws SQLException sqlexception异常
     */
    public static Map<String, String> getAllDbAndTabMap(Connection conn, String dbnames) throws SQLException {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Map<String, String> dbTabMap = null;
        try {
            String strSQL = "select tablename from pg_tables where schemaname = 'public'"; //获取所有的库
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();

            dbTabMap = new TreeMap<String, String>();
            while (rs.next()) {
                String table_name = rs.getString(1);
                if (dbTabMap.containsKey(dbnames)) {
                    dbTabMap.put(dbnames, dbTabMap.get(dbnames) + "," + table_name);
                } else {
                    dbTabMap.put(dbnames, table_name);
                }
            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            throw ex;
            //log.error("获取数据库中所有的库名表名出现异常");
        } finally {
            closeCon(rs, stmt, null);
        }
        return dbTabMap;
    }


    /**
     * 获取字段信息
     *
     * @param dbName    数据库名字
     * @param tableName 表名
     * @param conn      连接
     * @return {@link List}<{@link Map}<{@link String}, {@link String}>>
     */
    public static List<Map<String, String>> getTableFieldInfoBySchema(String dbName, String tableName, Connection conn) {
        List<Map<String, String>> fieldInfoList = null;
        Statement stmt = null;
        ResultSet rs = null;
        try {
            stmt = conn.createStatement();// 执行创建表
            String sql = "SELECT a.attnum, a.attname AS fieldname, t.typname AS type, a.attlen AS length, a.atttypmod AS lengthvar," +
                    " a.attnotnull AS notnull, b.description AS comment FROM pg_class c, pg_attribute a " +
                    "LEFT JOIN pg_description b ON a.attrelid = b.objoid AND a.attnum = b.objsubid, pg_type t " +
                    "WHERE c.relname = '"+tableName+"' " +
                    "AND a.attnum > 0 AND a.attrelid = c.oid AND a.atttypid = t.oid " +
                    "ORDER BY a.attnum";
            rs = stmt.executeQuery(sql);
            if (rs != null) {
                fieldInfoList = new ArrayList<>();
                while (rs.next()) {
                    Map<String, String> fieldInfoMap = new HashMap<>();
                    fieldInfoMap.put("fieldName", rs.getString("fieldname"));
                    fieldInfoMap.put("fieldCName", rs.getString("comment"));
                    fieldInfoMap.put("fieldType", rs.getString("type"));
                    fieldInfoList.add(fieldInfoMap);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, null);
        }
        return fieldInfoList;
    }

    public static List<Map<String, String>> getTableFieldInfoBySchema(String dbName, String tableName, Connection conn, List<String> tabFieldList) {
        List<Map<String, String>> fieldInfoList =new ArrayList<>();

        for (String fieldName : tabFieldList) {
            Statement stmt = null;
            try {
                stmt = conn.createStatement();
                ResultSet rs = stmt.executeQuery("SELECT a.attname as name, col_description (a.attrelid, a.attnum) as comment FROM pg_class as c,pg_attribute as a where a.attname= '" + fieldName + "' AND c.relname = '" + tableName + "' and a.attrelid = c.oid and a.attnum > 0 order by a.attname");
                if (rs != null) {
                    while (rs.next()) {
                        Map<String, String> fieldInfoMap = new HashMap<>();
                        fieldInfoMap.put("fieldName", rs.getString("name"));
                        fieldInfoMap.put("fieldCName", rs.getString("comment"));
                        fieldInfoList.add(fieldInfoMap);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        //closeCon(null, stmt, conn);
        return fieldInfoList;
    }


    /**
     * 获取数据库表数据
     *
     * @param conn    连接
     * @param dbname  dbname
     * @param tabname tabname
     * @param lineNum 行num
     * @return {@link List}<{@link String[]}>
     * @throws SQLException sqlexception异常
     */
    public static List<String[]> getTabDataList(Connection conn, String dbname, String tabname, Integer lineNum) throws SQLException {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<String[]> tabDataList = new ArrayList<String[]>();
        String strSQL = null;
        try {
            strSQL = "select * from " + tabname;
            if (lineNum != null && lineNum != 0)
                strSQL += " LIMIT " + lineNum;
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            ResultSetMetaData md = rs.getMetaData(); //获得结果集结构信息,元数据
            int columnCount = md.getColumnCount();   //获得列数
            while (rs.next()) {
                String[] row = new String[columnCount];
                for (int i = 0; i < columnCount; i++) {
                    row[i] = rs.getString(i + 1) == null ? "" : rs.getString(i + 1);
                }
                tabDataList.add(row);
            }
        } catch (Exception ex) {
            //System.out.println("获取数据库中所有的库名表名出现异常");
            log.error("获取数据库中所有的库名表名出现异常,执行sql：" + strSQL);
            //throw ex;
        } finally {
            closeCon(rs, stmt, null);
        }
        return tabDataList;
    }

    /**
     * 获取表信息
     *
     * @param dbName    数据库名字
     * @param tableName 表名
     * @param conn      连接
     * @return {@link Map}<{@link String}, {@link String}>
     */
    public static Map<String, String> getTableInfoBySchema(String dbName, String tableName, Connection conn) throws SQLException {
        Map<String, String> tableInfoMap = new HashMap<>();
        Statement stmt = null;
        ResultSet rs = null;
        String schema = conn.getSchema();
        try {
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery("SELECT current_database() AS database_name, relname AS table_name," +
                    "obj_description(oid, 'pg_class') AS table_comment FROM pg_class " +
                    "WHERE relkind = 'r' AND relname = '"+tableName+"' AND relnamespace = " +
                    "(SELECT oid FROM pg_namespace WHERE nspname = '"+schema+"')");
            if (rs != null && rs.next()) {
                if (rs.getString("database_name").equals(dbName)) {
                    tableInfoMap.put("tableName", rs.getString("table_name"));
                    tableInfoMap.put("tableCName", rs.getString("table_comment"));
                    tableInfoMap.put("tableRows", "");
                    tableInfoMap.put("dataSize", "");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs,stmt,null);
        }
        return tableInfoMap;
    }


    /**
     * 获取字段名称列表
     *
     * @param conn    连接
     * @param dbname  dbname
     * @param tabname tabname
     * @return {@link List}<{@link String}>
     * @throws SQLException sqlexception异常
     */
    public static List<String> getFieldNameList(Connection conn, String dbname, String tabname) throws SQLException {
        Statement stmt = null;
        ResultSet rs = null;
        List<String> list = new ArrayList<String>();
        try {
            String strSQL = "SELECT A.attname AS NAME FROM pg_class AS C, pg_attribute AS A WHERE C.relname='" + tabname + "' AND A.attrelid = C.oid AND A.attnum > 0";
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery(strSQL);
            while (rs.next()) {
                list.add(rs.getString(1));
            }
        } catch (Exception ex) {
            //log.error("获取数据库url:"+url+"库:"+dbname+"表:"+tabname+"中所有字段名出现异常");
            throw ex;
        } finally {
            closeCon(rs, stmt, null);
        }
        return list;
    }

    /**
     * @param objList：Map数据库集合
     * @param tableName：表名
     * @return Map<String, Object>：key为“sql”是批量插入语句，key为“params”是插入语句参数
     * @Description 通过Map数据集合生成批量插入SQL语句及插入语句参数（占位符形式）
     * <AUTHOR>
     * @date 2020年9月17日
     */
    @Override
    public Map<String, Object> getInsert2TableSqlAndPatams(int start, int end, List<Map<String, String>> objList, String dbname, String tableName) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO ").append(tableName).append(" (");
            for (String column : fields) {
                sb.append("\"" + column + "\"").append(", ");
            }
            String sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sb = new StringBuilder(sql);
            sb.append(") VALUES ");
            for (int i = start; i < end; i++) {
                Map<String, String> map = objList.get(i);
                sb.append("(");
                for (String key : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?, ");
                    params.add(map.get(key));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append("), ");
            }
            sql = sb.toString();
            lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
// sql += ";";
            sqlAndParams = new HashMap<>();
            sqlAndParams.put("sql", sql);
            sqlAndParams.put("params", params.toArray());
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    public static Map<String, Object> getInsertTableSqlAndPatams(int start, int end, List<Map<String, Object>> objList, String dbname, String tableName) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO ").append(tableName).append(" (");
            for (String column : fields) {
                sb.append("\"" + column + "\"").append(", ");
            }
            String sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sb = new StringBuilder(sql);
            sb.append(") VALUES ");
            for (int i = start; i < end; i++) {
                Map<String, Object> map = objList.get(i);
                sb.append("(");
                for (String key : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?, ");
                    params.add(map.get(key));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append("), ");
            }
            sql = sb.toString();
            lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
// sql += ";";
            sqlAndParams = new HashMap<>();
            sqlAndParams.put("sql", sql);
            sqlAndParams.put("params", params.toArray());
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    @Override
    public Map<String, Object> getInsert2TableSqlAndPatams(int start, int end, List<Map<String, String>> objList, String dbname, String tableName, String fieldnames) {
        return null;
    }

    @Override
    protected String getCreateTableSql(List<Map<String, String>> fieldInfoList, Map<String, String> obj, String tableName, List<String> maskfields) {
        return null;
    }


    /**
     * @param tableName：表名
     * @param dburl：数据库连接信息
     * @param username：数据库用户名
     * @param password：数据库密码
     * @return List<Map < String, String>>：数据库字段信息
     * @Description 查询表字段信息
     * <AUTHOR>
     * @date 2020年9月17日
     */
    @Override
    protected List<Map<String, String>> getTableFieldInfo(String dbName, String tableName, String dburl, String username,
                                                          String password) {
        List<Map<String, String>> fieldInfoList = null;
        Connection conn = null;
        Statement stmt = null;
        try {
            conn = getConn(dburl, username, password, "");// 打开连接
            stmt = conn.createStatement();// 执行创建表
            ResultSet rs = stmt.executeQuery("Select a.attnum,(select description from pg_catalog.pg_description where objoid=a.attrelid and objsubid=a.attnum) as descript ,a.attname,pg_catalog.format_type(a.atttypid,a.atttypmod) as data_type from pg_catalog.pg_attribute a where 1=1 and a.attrelid=(select oid from pg_class where relname='" + tableName + "' ) and a.attnum>0 and not a.attisdropped order by a.attnum;");
            if (rs != null) {
                fieldInfoList = new ArrayList<>();
                while (rs.next()) {
                    Map<String, String> fieldInfoMap = new HashMap<>();
                    fieldInfoMap.put("FieldEName", rs.getString("attname"));
                    fieldInfoMap.put("FieldType", rs.getString("data_type"));
                    fieldInfoMap.put("description", rs.getString("descript"));
                    fieldInfoList.add(fieldInfoMap);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        closeCon(null, stmt, conn);
        return fieldInfoList;
    }

    @Override
    protected Map<String, Object> getInsert2TableSqlAndPatams(List<Map<String, String>> objList, String dbName, String tableName) {
        return null;
    }

    /**
     * @param obj：Map对象
     * @param tableName：表名
     * @return String：生成的SQL语句
     * @Description 通过Map生成创建表SQL语句，自动检测字段名及类型
     * <AUTHOR>
     * @date 2020年9月17日
     */
    @Override
    protected String getCreateTableSql(List<Map<String, String>> fieldInfoList, Map<String, String> obj,
                                       String tableName, List<String> maskfields, String dbname, String watermarkField) {
        String sql = null;
        try {
            StringBuilder sb = new StringBuilder();
// sb.append("\r\nDROP TABLE IF EXISTS
// ").append("`").append(tableName).append("`").append(";\r\n");//删除表语句
            sb.append("CREATE TABLE ").append(tableName).append(" (\r\n");
// boolean firstId = true;
            for (Map<String, String> fieldInfo : fieldInfoList) {
                if (!obj.keySet().contains(fieldInfo.get("FieldEName"))) {// 跳过没有抽取的列
                    continue;
                }
                sb.append("\"" + fieldInfo.get("FieldEName") + "\"");// 字段名
                if (maskfields != null && maskfields.contains(fieldInfo.get("FieldEName"))) {// 加解密的字段类型更改为varchar
                    sb.append(" varchar(255)");// 类型
                } else {
                    sb.append(" varchar(255)");// 类型
                    //sb.append(" ").append(fieldInfo.get("FieldType"));// 类型
                }

				/*if ("auto_increment".equalsIgnoreCase(fieldInfo.get("Extra"))) {// 判断非空
					sb.append(" AUTO_INCREMENT");// 自增
				} else {
					if (fieldInfo.get("Default") != null) {
						sb.append(" DEFAULT '").append(fieldInfo.get("Default")).append("'");// 默认值
					} else {
						sb.append(" DEFAULT NULL");
					}
				}
				if ("PRI".equalsIgnoreCase(fieldInfo.get("Key"))) {
					sb.append(" PRIMARY KEY");// 主键
				}
				if (fieldInfo.get("Comment") != null && !"".equals(fieldInfo.get("Comment"))) {
					sb.append(" COMMENT '").append(fieldInfo.get("Comment")).append("'");
				}*/
                sb.append(",\n");
            }
            sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sql = sql + "\r)";
        } catch (Exception e) {
            e.printStackTrace();
            sql = null;
        }
        return sql;
    }


    /**
     * @Description: 拼接建表语句
     * <AUTHOR>
     * @date 2020年9月17日
     */
    public static String getCreateTableSql(String srcsql, List<Map<String, String>> fieldInfoList, Map<String, String> obj,
                                           String tableName, List<String> maskfields) {
        Set<String> keySet = obj.keySet();
        StringBuffer sb = new StringBuffer();
        StringBuffer end_sb = new StringBuffer();
        String end_sql = srcsql.substring(srcsql.lastIndexOf(")"), srcsql.length());
        String middle_sql = srcsql.substring(srcsql.indexOf("(") + 1, srcsql.lastIndexOf(")"));
        String[] srcfilemiddlesql = middle_sql.split(",");
        HashMap<String, String> srcfieldsql = new HashMap<String, String>();
        for (String s : srcfilemiddlesql) {
            String str = s.trim();
            String fieldname = str.substring(1, str.lastIndexOf("`"));
            if (str.startsWith("`")) {
                srcfieldsql.put(fieldname, str);
            } else {
                for (String extractfieldnamestr : keySet) {
                    if (str.contains(extractfieldnamestr)) {
                        end_sb.append(str).append(",\r\n");
                    }

                }
            }
        }
        sb.append("CREATE TABLE ").append(tableName).append(" (\r\n");
        for (Map<String, String> fieldInfo : fieldInfoList) {
            String field = fieldInfo.get("FieldEName");
            if (!keySet.contains(field)) {// 跳过没有抽取的列
                continue;
            }
            String fieldsql = srcfieldsql.get(field);
            if (maskfields != null && maskfields.contains(field)) {// 加解密的字段类型更改为varchar
                int post = appearNumber(fieldsql, " ", 2);
                String str = fieldsql.substring(post, fieldsql.length());
                sb.append(field);
                sb.append(" varchar(255) ");// 类型
                sb.append(str).append(",\r\n");
            } else {
                sb.append(fieldsql).append(",\r\n");
            }

        }
        sb.append(end_sb.toString());
        int lastIndex = sb.lastIndexOf(",");// 去掉最后一个逗号
        return sb.substring(0, lastIndex) + end_sql;
    }

    /**
     * @Description:获取表数据数量
     * <AUTHOR>
     * @date 2020-02-18
     */
    public static int getTabDataCount(Connection conn, String dbname, String tabname) {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        int count = 0;
        try {
            String strSQL = "select count(*) from " + "\"" + tabname + "\"";
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            while (rs.next()) {
                count = rs.getInt(1);
            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
        } finally {
            closeCon(rs, stmt, null);
        }
        return count;
    }


    /**
     * @Description: 获取字符串出现次数的位置
     * <AUTHOR>
     * @date 2020年9月17日
     */
    private static int appearNumber(String fieldsql, String s, int i) {
        Pattern pattern = Pattern.compile(s);
        Matcher findMatcher = pattern.matcher(fieldsql);
        int number = 0;
        while (findMatcher.find()) {
            number++;
            if (number == i) {//当“i”次出现时停止
                break;
            }
        }
        return findMatcher.start();
    }


}
