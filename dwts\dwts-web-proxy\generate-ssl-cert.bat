@echo off
echo 正在生成HTTPS自签名证书...

REM 检查是否存在keytool
where keytool >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到keytool命令，请确保已安装Java JDK
    pause
    exit /b 1
)

REM 生成自签名证书
keytool -genkeypair ^
    -alias tomcat ^
    -keyalg RSA ^
    -keysize 2048 ^
    -storetype PKCS12 ^
    -keystore src/main/resources/keystore.p12 ^
    -validity 365 ^
    -dname "CN=localhost, OU=DWTS, O=DWTS, L=Beijing, ST=Beijing, C=CN" ^
    -storepass changeit ^
    -keypass changeit

if %errorlevel% equ 0 (
    echo.
    echo ✅ SSL证书生成成功！
    echo 📁 证书位置: src/main/resources/keystore.p12
    echo 🔑 证书密码: changeit
    echo.
    echo 要启用HTTPS，请修改 application.yml：
    echo   server:
    echo     ssl:
    echo       enabled: true
    echo       key-store: classpath:keystore.p12
    echo       key-store-password: changeit
    echo       key-store-type: PKCS12
    echo       key-alias: tomcat
    echo.
    echo 启用后可通过以下地址访问：
    echo   https://localhost:9090/
    echo.
    echo ⚠️  注意：这是自签名证书，浏览器会显示安全警告，选择"继续访问"即可
) else (
    echo ❌ SSL证书生成失败
)

pause
