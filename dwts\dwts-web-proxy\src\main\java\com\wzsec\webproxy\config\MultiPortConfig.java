package com.wzsec.webproxy.config;

import com.wzsec.webproxy.domain.WebProxyConfig;
import com.wzsec.webproxy.service.WebProxyConfigService;
import com.wzsec.webproxy.util.PortChecker;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.Connector;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.File;
import java.io.InputStream;
import java.util.List;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;

/**
 * 多端口配置
 * 为每个代理配置创建额外的Tomcat连接器
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Slf4j
@Configuration
public class MultiPortConfig {

    @Autowired
    private WebProxyConfigService configService;

    @Autowired
    private ProxyPortsSslProperties sslProperties;

    @Autowired
    private ResourceLoader resourceLoader;

    @Bean
    public WebServerFactoryCustomizer<TomcatServletWebServerFactory> containerCustomizer() {
        return factory -> {
            log.info("开始配置多端口监听...");

            try {
                // 从数据库获取代理配置（延迟获取）
                if (configService != null) {
                    List<WebProxyConfig> configs = configService.getAllActiveConfigs();
                    log.info("从数据库获取到{}个代理配置", configs.size());

                    for (WebProxyConfig config : configs) {
                        int port = config.getProxyPort();

                        // 跳过主应用端口
                        if (port == 9090) {
                            log.info("跳过主应用端口: {}", port);
                            continue;
                        }

                        // 创建额外的连接器
                        Connector connector = new Connector("org.apache.coyote.http11.Http11NioProtocol");
                        connector.setPort(port);

                        // 根据配置决定是否启用HTTPS
                        if (sslProperties.isValidSslConfig()) {
                            log.info("为端口{}配置HTTPS支持", port);

                            // 检查证书文件是否存在
                            if (isKeystoreFileExists()) {
                                configureHttpsConnector(connector);
                                log.info("端口{}已配置HTTPS支持", port);
                            } else {
                                log.warn("SSL证书文件不存在: {}，端口{}将使用HTTP",
                                        sslProperties.getKeystoreFile(), port);
                                configureHttpConnector(connector);
                            }
                        } else {
                            log.info("为端口{}配置HTTP支持", port);
                            configureHttpConnector(connector);
                        }

                        // 设置连接器属性
                        connector.setProperty("maxThreads", "200");
                        connector.setProperty("maxConnections", "8192");
                        connector.setProperty("acceptCount", "100");
                        connector.setProperty("connectionTimeout", "20000");

                        factory.addAdditionalTomcatConnectors(connector);

                        log.info("添加代理端口连接器: {} -> {}:{} (端口:{})",
                                config.getProxyName(),
                                config.getTargetHost(),
                                config.getTargetPort(),
                                port);
                    }

                    log.info("多端口配置完成，共配置{}个额外端口", configs.size());
                } else {
                    log.warn("配置服务未就绪，将在应用启动后动态配置端口");
                }

            } catch (Exception e) {
                log.error("配置多端口失败，将使用单端口模式", e);
            }
        };
    }

    /**
     * 配置HTTP连接器
     */
    private void configureHttpConnector(Connector connector) {
        connector.setScheme("http");
        connector.setSecure(false);
    }

    /**
     * 检查证书文件是否存在
     */
    private boolean isKeystoreFileExists() {
        try {
            String keystoreFile = sslProperties.getKeystoreFile();

            if (keystoreFile.startsWith("classpath:")) {
                // 处理classpath资源
                Resource resource = resourceLoader.getResource(keystoreFile);
                boolean exists = resource.exists();
                log.debug("检查classpath资源: {} - 存在: {}", keystoreFile, exists);
                return exists;
            } else {
                // 处理文件系统路径
                File file = new File(keystoreFile);
                boolean exists = file.exists();
                log.debug("检查文件系统路径: {} - 存在: {}", keystoreFile, exists);
                return exists;
            }
        } catch (Exception e) {
            log.error("检查证书文件时发生错误", e);
            return false;
        }
    }

    /**
     * 配置HTTPS连接器
     */
    private void configureHttpsConnector(Connector connector) {
        connector.setScheme("https");
        connector.setSecure(true);

        // 设置SSL属性
        connector.setProperty("SSLEnabled", "true");

        // 处理证书文件路径
        String keystoreFile = sslProperties.getKeystoreFile();
        if (keystoreFile.startsWith("classpath:")) {
            // 对于classpath资源，需要转换为绝对路径
            try {
                Resource resource = resourceLoader.getResource(keystoreFile);
                keystoreFile = resource.getFile().getAbsolutePath();
                log.debug("转换classpath路径为绝对路径: {}", keystoreFile);
            } catch (Exception e) {
                log.error("无法转换classpath路径: {}", keystoreFile, e);
                // 保持原路径，让Tomcat尝试处理
            }
        }

        connector.setProperty("keystoreFile", keystoreFile);
        connector.setProperty("keystorePass", sslProperties.getKeystorePassword());
        connector.setProperty("keystoreType", sslProperties.getKeystoreType());
        connector.setProperty("keyAlias", sslProperties.getKeyAlias());
        connector.setProperty("clientAuth", "false");
        connector.setProperty("protocol", "TLS");
        connector.setProperty("sslProtocol", "TLS");

        log.debug("HTTPS连接器配置完成 - 证书文件: {}, 类型: {}, 别名: {}",
                keystoreFile,
                sslProperties.getKeystoreType(),
                sslProperties.getKeyAlias());
    }
}
