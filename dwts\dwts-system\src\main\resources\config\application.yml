server:
  port: 8700

spring:
  freemarker:
    check-template-location: false
  profiles:
    active: dev
  jackson:
    time-zone: GMT+8
  data:
    redis:
      repositories:
        enabled: false

  redis:
    #数据库索引
    database: 0
    host: 127.0.0.1
    port: 6379
    # 集群环境打开下面注释，单机不需要打开
#    cluster:
#    nodes:
#      - *************:7001
#      - **************:7001
#      - *************:7001
    password:
    #连接超时时间
    timeout: 5000

task:
  pool:
    # 核心线程池大小
    core-pool-size: 10
    # 最大线程数
    max-pool-size: 30
    # 活跃时间
    keep-alive-seconds: 60
    # 队列容量
    queue-capacity: 50

#七牛云
qiniu:
  # 文件大小 /M
  max-size: 15

#邮箱验证码有效时间/分钟
code:
  expiration: 5

#登录图形验证码有效时间/分钟
loginCode:
  expiration: 2

#密码加密传输，前端公钥加密，后端私钥解密
rsa:
  private_key: MIIBUwIBADANBgkqhkiG9w0BAQEFAASCAT0wggE5AgEAAkEA0vfvyTdGJkdbHkB8mp0f3FE0GYP3AYPaJF7jUd1M0XxFSE2ceK3k2kw20YvQ09NJKk+OMjWQl9WitG9pB6tSCQIDAQABAkA2SimBrWC2/wvauBuYqjCFwLvYiRYqZKThUS3MZlebXJiLB+Ue/gUifAAKIg1avttUZsHBHrop4qfJCwAI0+YRAiEA+W3NK/RaXtnRqmoUUkb59zsZUBLpvZgQPfj1MhyHDz0CIQDYhsAhPJ3mgS64NbUZmGWuuNKp5coY2GIj/zYDMJp6vQIgUueLFXv/eZ1ekgz2Oi67MNCk5jeTF2BurZqNLR3MSmUCIFT3Q6uHMtsB9Eha4u7hS31tj1UWE+D+ADzp59MGnoftAiBeHT7gDMuqeJHPL4b+kC+gzV4FGTfhR9q3tTbklZkD2A==

# sm.ms 图床的 token
smms:
  token: 1oOP3ykFDI0K6ifmtvU7c8Y1eTWZSlyl
