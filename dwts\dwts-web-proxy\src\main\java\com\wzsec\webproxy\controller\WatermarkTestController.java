package com.wzsec.webproxy.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 水印测试控制器
 * 用于测试水印功能是否正常工作
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Slf4j
@RestController
@RequestMapping("/api/watermark/test")
public class WatermarkTestController {

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "OK");
        response.put("message", "水印服务运行正常");
        response.put("timestamp", System.currentTimeMillis());
        
        log.info("水印服务健康检查 - 状态正常");
        
        return ResponseEntity.ok(response);
    }

    /**
     * 简单的水印检测测试
     */
    @PostMapping("/simple-detect")
    public ResponseEntity<Map<String, Object>> simpleDetect(@RequestBody String content) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("收到简单检测请求 - 内容长度: {}", content.length());
            
            // 简单检测零宽字符
            boolean hasZeroWidthChars = containsZeroWidthCharacters(content);
            int zeroWidthCount = countZeroWidthCharacters(content);
            
            Map<String, Object> result = new HashMap<>();
            result.put("hasZeroWidthChars", hasZeroWidthChars);
            result.put("zeroWidthCount", zeroWidthCount);
            result.put("contentLength", content.length());
            result.put("analysis", analyzeContent(content));
            
            response.put("success", true);
            response.put("data", result);
            response.put("message", hasZeroWidthChars ? 
                String.format("检测到 %d 个零宽字符", zeroWidthCount) : "未检测到零宽字符");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("简单检测失败", e);
            response.put("success", false);
            response.put("message", "检测失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 生成测试用的水印内容
     */
    @PostMapping("/generate-test-content")
    public ResponseEntity<Map<String, Object>> generateTestContent(@RequestBody String originalContent) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 手动插入一些零宽字符作为测试
            String testContent = insertTestWatermark(originalContent);
            
            Map<String, Object> result = new HashMap<>();
            result.put("original", originalContent);
            result.put("watermarked", testContent);
            result.put("originalLength", originalContent.length());
            result.put("watermarkedLength", testContent.length());
            result.put("addedChars", testContent.length() - originalContent.length());
            
            response.put("success", true);
            response.put("data", result);
            response.put("message", "测试水印内容生成成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("生成测试内容失败", e);
            response.put("success", false);
            response.put("message", "生成失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 检测内容是否包含零宽字符
     */
    private boolean containsZeroWidthCharacters(String content) {
        if (content == null) return false;
        
        for (char c : content.toCharArray()) {
            if (isZeroWidthCharacter(c)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 统计零宽字符数量
     */
    private int countZeroWidthCharacters(String content) {
        if (content == null) return 0;
        
        int count = 0;
        for (char c : content.toCharArray()) {
            if (isZeroWidthCharacter(c)) {
                count++;
            }
        }
        return count;
    }

    /**
     * 判断是否是零宽字符
     */
    private boolean isZeroWidthCharacter(char c) {
        return c == '\u200B' ||  // ZERO_WIDTH_SPACE
               c == '\u200C' ||  // ZERO_WIDTH_NON_JOINER
               c == '\u200D' ||  // ZERO_WIDTH_JOINER
               c == '\u2060' ||  // WORD_JOINER
               c == '\u2062';    // INVISIBLE_SEPARATOR
    }

    /**
     * 分析内容
     */
    private Map<String, Object> analyzeContent(String content) {
        Map<String, Object> analysis = new HashMap<>();
        Map<String, Integer> charStats = new HashMap<>();
        
        for (char c : content.toCharArray()) {
            String charName = getZeroWidthCharName(c);
            if (charName != null) {
                charStats.merge(charName, 1, Integer::sum);
            }
        }
        
        analysis.put("zeroWidthCharStats", charStats);
        analysis.put("totalZeroWidthChars", charStats.values().stream().mapToInt(Integer::intValue).sum());
        
        return analysis;
    }

    /**
     * 获取零宽字符名称
     */
    private String getZeroWidthCharName(char c) {
        switch (c) {
            case '\u200B': return "ZERO_WIDTH_SPACE";
            case '\u200C': return "ZERO_WIDTH_NON_JOINER";
            case '\u200D': return "ZERO_WIDTH_JOINER";
            case '\u2060': return "WORD_JOINER";
            case '\u2062': return "INVISIBLE_SEPARATOR";
            default: return null;
        }
    }

    /**
     * 插入测试水印
     */
    private String insertTestWatermark(String content) {
        if (content == null || content.isEmpty()) {
            return content;
        }
        
        StringBuilder result = new StringBuilder();
        
        // 在每个单词后插入零宽字符
        String[] words = content.split("\\s+");
        for (int i = 0; i < words.length; i++) {
            result.append(words[i]);
            
            // 插入测试用的零宽字符序列
            if (i < words.length - 1) {
                result.append('\u200B'); // ZERO_WIDTH_SPACE
                result.append(' '); // 正常空格
                if (i % 3 == 0) {
                    result.append('\u200C'); // ZERO_WIDTH_NON_JOINER
                }
                if (i % 5 == 0) {
                    result.append('\u200D'); // ZERO_WIDTH_JOINER
                }
            }
        }
        
        return result.toString();
    }
}
