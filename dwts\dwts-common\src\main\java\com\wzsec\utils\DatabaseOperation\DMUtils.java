package com.wzsec.utils.DatabaseOperation;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.sql.*;
import java.util.*;


@Slf4j
public class DMUtils extends DatabaseOperationUtil {

    private static String JDBC_DRIVER = "dm.jdbc.driver.DmDriver";

    @Override
    public Connection getConnection(String url, String username, String password) throws SQLException, ClassNotFoundException {
        Class.forName(JDBC_DRIVER);
        Connection con = DriverManager.getConnection(url, username, password);
        return con;
    }

    @Override
    public List<Map<String, String>> getResultSet(Connection conn, String dbName, String tableName, String condition,
                                                  Long pageSize, long StartLine, long endLine) throws SQLException {

        List<String> tabFieldList = getTabFieldList(conn, dbName, tableName);
        ResultSet rs = null;
        Statement stmt = null;
        String sql = "";
        if (org.apache.commons.lang3.StringUtils.isNotBlank(condition)) {
            String template = "select * from (select * from {}.{} {}) as a limit {},{}";
            sql = StrUtil.format(template, dbName, tableName, condition, StartLine, endLine);
        } else {
            String template = "select * from {}.{} limit {},{}";
            sql = StrUtil.format(template, dbName, tableName, StartLine, endLine);
        }

        // 将结果集转换为 List<Map>
        List<Map<String, String>> dataList = new ArrayList<>();
        try {
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();

            while (rs.next()) {
                Map<String, String> rowMap = new LinkedHashMap<>(); // 保持插入顺序

                for (String fieldName : tabFieldList) {
                    for (int i = 1; i <= columnCount; i++) {
                        String columnName = metaData.getColumnName(i);
                        if (fieldName.equalsIgnoreCase(columnName)) {
                            Object columnValue = rs.getObject(i);
                            rowMap.put(columnName, String.valueOf(columnValue));
                            break;
                        }
                    }
                }

                dataList.add(rowMap);
            }
        } finally {
            DatabaseOperationUtil.closeCon(rs, stmt, null);
        }
        return dataList;
    }

    @Override
    public Map<String, String> getAllDbTabMap(Connection conn, String dbName) throws SQLException {
        Statement stmt = null;
        ResultSet rs = null;
        Map<String, String> dbTabMap = new HashMap<String, String>();
        try {
            String strSQL = "select owner,TABLE_NAME from all_tables ";
            if (dbName != null && !"".equals(dbName)) {
                strSQL += " where owner in (" + "'" + dbName + "'" + ") ";
            }
            conn.setAutoCommit(false);
            stmt = conn.createStatement();

            rs = stmt.executeQuery(strSQL);
            while (rs.next()) {
                String dbNames = rs.getString("owner");// 库名
                String tabName = rs.getString("TABLE_NAME");// 表名
                if (!tabName.contains("##") && !tabName.contains("$")) {  //排除系统表
                    if (dbTabMap.containsKey(dbNames)) {
                        dbTabMap.put(dbNames, dbTabMap.get(dbNames) + "," + tabName);
                    } else {
                        dbTabMap.put(dbNames, tabName);
                    }
                }
            }
            conn.commit();
        } finally {
            DatabaseOperationUtil.closeCon(rs, stmt, null);
        }
        return dbTabMap;
    }

    @Override
    public Map<String, String> getTableInfoBySchema(Connection conn, String dbName, String tableName) throws SQLException {
        Map<String, String> tableInfoMap = new HashMap<>();
        Statement stmt = null;
        ResultSet rs = null;
        try {
            stmt = conn.createStatement();// 执行创建表
            String sql = " select tvname AS tabname, comment$ from SYSTABLECOMMENTS where schname = '" + dbName + "' AND tvname = '" + tableName + "' order by tvname ";
            rs = stmt.executeQuery(sql);
            if (rs != null && rs.next()) {
                if (rs.getString("tabname").equals(tableName)) {
                    tableInfoMap.put("tableName", rs.getString("tabname"));
                    tableInfoMap.put("tableCName", rs.getString("comment$"));
                }
            } else {
                tableInfoMap.put("tableName", tableName);
                tableInfoMap.put("tableCName", "");
            }
        } finally {
            DatabaseOperationUtil.closeCon(rs, stmt, null);
        }
        return tableInfoMap;
    }

    @Override
    public List<String> getTabFieldList(Connection conn, String dbname, String tableName) throws SQLException {
        Statement stmt = null;
        ResultSet rs = null;
        List<String> fieldList = new ArrayList<String>();
        try {
            String sql = "select COLUMN_NAME from all_tab_columns  where table_name= '" + tableName + "' and owner ='" + dbname + "' ";
            conn.setAutoCommit(false);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            conn.commit();
            while (rs.next()) {
                fieldList.add(rs.getString(1));
            }
        } finally {
            DatabaseOperationUtil.closeCon(rs, stmt, null);
        }
        return fieldList;
    }

    @Override
    public List<Map<String, String>> getTableFieldInfo(Connection conn, String dbName, String tableName) throws SQLException {
        List<Map<String, String>> fieldInfoList = null;
        Statement stmt = null;
        ResultSet rs = null;
        try {
            stmt = conn.createStatement();// 执行创建表
            String template = "select COLUMN_NAME,DATA_TYPE,DATA_LENGTH,DATA_PRECISION,DATA_SCALE,NULLABLE,COLUMN_ID from all_tab_columns  where table_name='{}' and owner='{}' ";
            String sql = StrUtil.format(template, tableName, dbName);
            rs = stmt.executeQuery(sql);
            if (rs != null) {
                fieldInfoList = new ArrayList<>();

                while (rs.next()) {
                    Map<String, String> fieldInfoMap = new HashMap<>();
                    fieldInfoMap.put("COLUMN_NAME", rs.getString("COLUMN_NAME"));// 字段名
                    fieldInfoMap.put("DATA_TYPE", rs.getString("DATA_TYPE"));// 字段类型
                    fieldInfoMap.put("DATA_LENGTH", rs.getString("DATA_LENGTH"));// 数据长度
                    if (rs.getString("DATA_PRECISION") == null) {
                        fieldInfoMap.put("DATA_PRECISION", "");
                    } else {
                        fieldInfoMap.put("DATA_PRECISION", rs.getString("DATA_PRECISION"));// 数据长度(和上方不同)
                    }
                    if (rs.getString("DATA_SCALE") == null) {
                        fieldInfoMap.put("DATA_SCALE", "");
                    } else {
                        fieldInfoMap.put("DATA_SCALE", rs.getString("DATA_SCALE"));// 获取数据精度
                    }
                    fieldInfoMap.put("NULLABLE", rs.getString("NULLABLE"));// 获取是否为空
                    fieldInfoMap.put("COLUMN_ID", rs.getString("COLUMN_ID"));// 字段序号
                    fieldInfoList.add(fieldInfoMap);
                }
            }
        } finally {
            DatabaseOperationUtil.closeCon(rs, stmt, null);
        }

        return fieldInfoList;
    }

    @Override
    public int getDataCount(Connection conn, String in_dbname, String in_tabname, String in_limitingcondition) throws SQLException {
        int count = 0;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        String sql = "";
        if (org.apache.commons.lang3.StringUtils.isNotBlank(in_limitingcondition)) {
            String template = "select count(*) from (select * from {}.{} {}) as a";
            sql = StrUtil.format(template, in_dbname, in_tabname, in_limitingcondition);
        } else {
            String template = "select count(*) from {}.{}";
            sql = StrUtil.format(template, in_dbname, in_tabname);
        }
        log.info("连接dm数据库成功");
        preparedStatement = conn.prepareStatement(sql);
        resultSet = preparedStatement.executeQuery();
        try {
            preparedStatement = conn.prepareStatement(sql);
            resultSet = preparedStatement.executeQuery();

            while (resultSet.next()) {
                count = (int) resultSet.getLong(1);
            }
        } finally {
            DatabaseOperationUtil.closeCon(resultSet, preparedStatement, null);
        }
        return count;
    }


    @Override
    public String getCreateTableSql(String dbName, String tableName,
                                    List<Map<String, String>> fieldInfoList,
                                    Map<String, String> obj,
                                    List<String> maskfields, String srcSql) {
        String sql = null;
        try {
            StringBuilder sb = new StringBuilder();
            // sb.append("\r\nDROP TABLE IF EXISTS
            // ").append("`").append(tableName).append("`").append(";\r\n");//删除表语句
            sb.append("CREATE TABLE \"" + dbName + "\".\"").append(tableName).append("\" (\r\n");
            // boolean firstId = true;
            for (Map<String, String> fieldInfo : fieldInfoList) {
                //if (!obj.keySet().contains(fieldInfo.get("COLUMN_NAME"))) {// 跳过没有抽取的列
                //    continue;
                //}
                sb.append("\"").append(fieldInfo.get("COLUMN_NAME")).append("\"");// 字段名
                if (maskfields != null && maskfields.contains(fieldInfo.get("COLUMN_NAME"))) {// 脱敏的字段类型更改为varchar
                    sb.append(" varchar(255)");// 类型
                } else {
                    sb.append(" ").append(fieldInfo.get("DATA_TYPE"));// 类型
                    if (fieldInfo.get("DATA_TYPE").contains("CHAR") || fieldInfo.get("DATA_TYPE").contains("char")) {
                        sb.append(" ").append("(" + fieldInfo.get("DATA_LENGTH") + ")");// 长度
                    }
                }
                sb.append(",\n");
            }
            sql = sb.toString();

            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sql = sql + "\r)";
            //MAIN:数据库默认的表空间
            sql = sql + "STORAGE(ON \"" + "MAIN" + "\", CLUSTERBTR);";
        } catch (Exception e) {
            e.printStackTrace();
            sql = null;
        }
        return sql;
    }

    @Override
    public Map<String, Object> getBatchInsertionInformation(int start, int end, String dbname, String tableName, List<Map<String, String>> objList) {
        Map<String, Object> sqlAndParams = null;
        String aa = "\"";
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT ALL ");
            for (int i = start; i < end; i++) {
                Map<String, String> map = objList.get(i);
                sb.append(" INTO  " + "\"" + dbname + "\"" + "." + aa).append(tableName).append(aa + " (");
                for (String column : fields) {
                    sb.append("" + aa).append(column).append(aa + ", ");
                }
                String sql = sb.toString();
                int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append(") VALUES ");
                sb.append("(");
                for (String column : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("");
                    sb.append("?");
                    sb.append(", ");
                    params.add(map.get(column));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append(")");
                sql = sb.toString();
                // lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sql += " )SELECT 1 FROM DUAL";
                sqlAndParams = new HashMap<>();
                sqlAndParams.put("sql", sql);
                sqlAndParams.put("params", params.toArray());
            }
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    @Override
    public List<String[]> getTabDataList(Connection conn, String dbname, String tableName, Integer lineNum) throws SQLException {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<String[]> tabDataList = new ArrayList<String[]>();
        try {
            String strSQL = "select * from " + "\"" + dbname + "\"" + "." + "\"" + tableName + "\"";
            if (lineNum != null && lineNum != 0)
                strSQL += " where rownum <= " + lineNum + " order by rownum";
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            ResultSetMetaData md = rs.getMetaData(); //获得结果集结构信息,元数据
            int columnCount = md.getColumnCount();   //获得列数
            while (rs.next()) {
                String[] row = new String[columnCount];
                for (int i = 0; i < columnCount; i++) {
                    row[i] = rs.getString(i + 1);
                }
                tabDataList.add(row);
            }
        } finally {
            DatabaseOperationUtil.closeCon(rs, stmt, null);
        }
        return tabDataList;
    }
}
