package com.wzsec.utils.database;

import cn.hutool.core.util.StrUtil;
import com.mongodb.client.MongoClient;
import com.wzsec.utils.Const;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;

import java.io.IOException;
import java.sql.*;
import java.util.*;


/**
 * 数据库工具类
 *
 * <AUTHOR>
 * @Description
 * @date 2019年10月15日 上午9:43:42
 */
@Slf4j
public abstract class DatabaseUtil {

    /**
     * 获取数据库连接
     *
     * @param jdbcName 数据库驱动
     * @param url      数据库url
     * @param username 数据库用户名
     * @param password 数据库密码
     * @return Connection 数据库连接
     * <AUTHOR>
     * @date 2019年10月15日 上午9:46:17
     */
    public static Connection getConn(String jdbcName, String url, String username, String password) {
        try {
            Class.forName(jdbcName);//oracle.jdbc.OracleDriver
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
        Connection con = null;
        try {
            con = DriverManager.getConnection(url, username, password);//*******************************************
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return con;
    }

    public static void main(String[] args) {
        getConn("oracle.jdbc.OracleDriver","*******************************************",
                "demo","demo");
    }


    /**
     * 获取数据库连接
     *
     * @param type     数据库类型
     * @param url      数据库url
     * @param username 数据库用户名
     * @param password 数据库密码
     * @param dbnames  数据库库名
     * @return {@link Object}
     * @throws SQLException sqlexception异常
     * <AUTHOR>
     * @date 2020-4-22
     */
    public static Object getConn(String type, String url, String username, String password, String dbnames) throws Exception {
        Object conn = null;
        try {
            if (
                    type.equalsIgnoreCase("mysql")          || type.equalsIgnoreCase("oracle")          ||
                    type.equalsIgnoreCase("gbase")          || type.equalsIgnoreCase("sqlserver")       ||
                    type.equalsIgnoreCase("dm")             || type.equalsIgnoreCase("mariadb")         ||
                    type.equalsIgnoreCase(Const.DB_POSTGRESQL)          || type.equalsIgnoreCase(Const.DB_GAUSS)                ||
                    type.equalsIgnoreCase(Const.DB_GAUSSDB_OPENGAUSS)   || type.equalsIgnoreCase(Const.DB_GAUSSDB_MYSQL)        ||
                    type.equalsIgnoreCase(Const.DB_BYTEHOSE)            || type.equalsIgnoreCase(Const.DB_HIGHGO)

            ) {
                String jdbcDriver = getDriversByType(type);
                conn = getConn(jdbcDriver, url, username, password);
            } else if (type.equalsIgnoreCase("hive")) {
                //进行Kerberos认证
                try {
                    //AuthKrb5.kerberosConfig3();
                } catch (Exception e) {
                    log.error("kerberos认证失败");
                }
                String jdbcDriver = getDriversByType(type);
                conn = HiveUtil.getConn(jdbcDriver, username, password, url);
            } else if (type.equalsIgnoreCase("hbase")) {
                conn = HBaseUtil.getConn(url);
            } else if (type.equalsIgnoreCase(Const.DB_MONGODB)) {
                conn = MongoDBUtil.getConnect(url, username, password, dbnames);
            }
        } catch (Exception ex) {
            log.error("获取数据库:" + dbnames + "中所有库名表名出现异常");
            throw ex;
        }
        return conn;
    }


    /**
     * 根据数据源类型获取连接驱动
     *
     * @param dbType 数据源类型
     */
    public static String getDriversByType(String dbType) {
        String jdbc_driver = "";
        if (dbType.equalsIgnoreCase("oracle")) {
            jdbc_driver = "oracle.jdbc.OracleDriver";
        } else if (dbType.equalsIgnoreCase("mysql")) {
            jdbc_driver = "com.mysql.jdbc.Driver";
        } else if (dbType.equalsIgnoreCase("informix")) {
            jdbc_driver = "com.informix.jdbc.IfxDriver";
        } else if (dbType.equalsIgnoreCase("mariadb")) {
            jdbc_driver = "org.mariadb.jdbc.Driver";
        } else if (dbType.equalsIgnoreCase("gbase")) {
            jdbc_driver = "com.gbase.jdbc.Driver";
        } else if (dbType.equalsIgnoreCase("sqlserver")) {
            jdbc_driver = "com.microsoft.sqlserver.jdbc.SQLServerDriver";
        } else if (dbType.equalsIgnoreCase("db2")) {
            jdbc_driver = "com.ibm.db2.jcc.DB2Driver";
        } else if (dbType.equalsIgnoreCase("hive") || dbType.equalsIgnoreCase("hive2")) {
            jdbc_driver = "org.apache.hive.jdbc.HiveDriver";
        } else if (dbType.equalsIgnoreCase("postgresql")) {
            jdbc_driver = "org.postgresql.Driver";
        } else if (dbType.equalsIgnoreCase("dm")) {
            jdbc_driver = "dm.jdbc.driver.DmDriver";
        } else if (dbType.equalsIgnoreCase(Const.DB_POSTGRESQL) || dbType.equalsIgnoreCase(Const.DB_GAUSS) || dbType.equalsIgnoreCase(Const.DB_GAUSSDB_OPENGAUSS)   || dbType.equalsIgnoreCase(Const.DB_GAUSSDB_MYSQL)) {//高斯数据库默认使用PG数据源，驱动程序和PG一致
            jdbc_driver = "org.postgresql.Driver";
        } else if (dbType.equalsIgnoreCase(Const.DB_BYTEHOSE)) {
            jdbc_driver = "com.clickhouse.jdbc.ClickHouseDriver";
        } else if (dbType.equalsIgnoreCase(Const.DB_HIGHGO)) {
            jdbc_driver = "com.highgo.jdbc.Driver";
        }
        return jdbc_driver;
    }

    /**
     * 释放连接
     *
     * @param rs   结果集
     * @param st   预编译的SQL语句的对象
     * @param conn 连接对象
     * <AUTHOR>
     * @date 2019年10月15日 上午9:46:42
     */
    public static void closeCon(ResultSet rs, Statement st, Connection conn) {
        try {
            if (rs != null) {
                rs.close(); // 关闭结果集
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            try {
                if (st != null) {
                    st.close(); // 关闭Statement
                }
            } catch (SQLException e) {
                e.printStackTrace();
            } finally {
                try {
                    if (conn != null) {
                        conn.close(); // 关闭连接
                    }
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
    }


    /**
     * 获取修改表名SQL
     *
     * @param dbType            数据库类型
     * @param dbName            数据库名
     * @param originalTableName 原始表名
     * @param newTableName      新表名
     * @return {@link String}
     */
    public static String modifyingTableName(String dbType, String dbName, String originalTableName, String newTableName) {
        String originalTableBackupSql = "";
        if (dbType.equalsIgnoreCase("mysql") || dbType.equalsIgnoreCase("dm") ||
                dbType.equalsIgnoreCase("mariadb")) {
            String template = "ALTER TABLE {}.{} RENAME TO {}";
            originalTableBackupSql = StrUtil.format(template, dbName, originalTableName, newTableName);
        } else if (dbType.equalsIgnoreCase("oracle")) {
            String template = "ALTER TABLE \"{}\".\"{}\" RENAME TO \"{}\"";
            originalTableBackupSql = StrUtil.format(template, dbName, originalTableName, newTableName);
        } else if (dbType.equalsIgnoreCase("sqlserver")) {
            String template = "ALTER TABLE {}.{} RENAME TO {}";
            originalTableBackupSql = StrUtil.format(template, dbName, originalTableName, newTableName);
        } else if (dbType.equalsIgnoreCase("gbase")) {
            String template = "ALTER TABLE {}.{} RENAME TO {}";
            originalTableBackupSql = StrUtil.format(template, dbName, originalTableName, newTableName);
        } else if (dbType.equalsIgnoreCase("hive")) {
            //进行Kerberos认证
            try {
                //AuthKrb5.kerberosConfig3();
            } catch (Exception e) {
                log.error("kerberos认证失败");
            }
            String template = "ALTER TABLE {}.{} RENAME TO {}";
            originalTableBackupSql = StrUtil.format(template, dbName, originalTableName, newTableName);
        } else if (dbType.equalsIgnoreCase("hbase")) {
            String template = "ALTER TABLE {}.{} RENAME TO {}";
            originalTableBackupSql = StrUtil.format(template, dbName, originalTableName, newTableName);
        } else if (dbType.equalsIgnoreCase(Const.DB_MONGODB) || dbType.equalsIgnoreCase(Const.DB_GAUSS) || dbType.equalsIgnoreCase(Const.DB_HIGHGO)) {//高斯数据库默认使用PG数据源，驱动程序和PG一致
            String template = "ALTER TABLE \"{}\".\"{}\" RENAME TO \"{}\"";
            originalTableBackupSql = StrUtil.format(template, dbName, originalTableName, newTableName);
        }
        return originalTableBackupSql;
    }

    /**
     * 创建新表并批量插入数据到新表
     *
     * @param inputDBName     输入库名
     * @param inputtableName  输入表名
     * @param inputdburl      输入数据库连接url
     * @param inputusername   输入数据库用户名
     * @param inputpassword   输入数据库密码
     * @param objList         Map数据集合
     * @param outputtableName 输出新表名称
     * @param outputdburl     输出数据库连接url
     * @param outputusername  输出数据库用户名
     * @param outputpassword  输出数据库密码
     * @param maskfields      脱敏字段
     * @return Boolean：true成功，false失败
     * <AUTHOR>
     * @date 2019年10月15日 下午3:36:16
     */
    public static Boolean insertData2NewTable(String inputDBName, String inputtableName, String inputdburl, String inputusername,
                                              String inputpassword, List<Map<String, String>> objList, String outputtableName,String outputDBName, String outputdburl,
                                              String outputusername, String outputpassword, List<String> maskfields) {
        DatabaseUtil databaseUtil = null;
        String jdbc_driver = null;
        if (inputdburl.contains("oracle") || inputdburl.contains("ORACLE")) {
            databaseUtil = new OracleUtil();
            jdbc_driver = "oracle.jdbc.OracleDriver";
        } else if (inputdburl.contains("mysql") || inputdburl.contains("MYSQL")) {
            databaseUtil = new MysqlUtil();
            jdbc_driver = "com.mysql.jdbc.Driver";
        } else if (inputdburl.contains("gbase") || inputdburl.contains("GBASE")) {
            databaseUtil = new GbaseUtil();
            jdbc_driver = "com.gbase.jdbc.Driver";
        } else if (inputdburl.contains("sqlserver") || inputdburl.contains("SQLSERVER")) {
            databaseUtil = new SQLServerUtil();
            jdbc_driver = "com.microsoft.sqlserver.jdbc.SQLServerDriver";
        } else if (inputdburl.contains("db2") || inputdburl.contains("DB2")) {
            databaseUtil = new DB2Util();
            jdbc_driver = "com.ibm.db2.jcc.DB2Driver";
        } else if (inputdburl.contains("hive2") || inputdburl.contains("HIVE2")) {
            databaseUtil = new HiveUtil();
            jdbc_driver = "org.apache.hive.jdbc.HiveDriver";
        }
        if (databaseUtil == null || jdbc_driver == null) {
            return false;
        }
        String createTableSql = null;


        if (inputdburl.contains("mysql") || inputdburl.contains("MYSQL")) {
            String srcCreatetablesql = MysqlUtil.getSrcCreateTableSql(inputtableName, inputdburl,
                    inputusername, inputpassword);
            List<Map<String, String>> fieldInfoList = databaseUtil.getTableFieldInfo(inputDBName, inputtableName, inputdburl, inputusername, inputpassword);
            if ("".equals(srcCreatetablesql)) {
                log.info("1.获取原表字段信息失败");
                return false;
            }
            log.info("1.获取原表字段信息成功");
            createTableSql = MysqlUtil.getCreateTableSql(srcCreatetablesql, fieldInfoList, objList.get(0), outputtableName,outputDBName,
                    maskfields, null);

        } else {
            // 1.读取源表结构
            List<Map<String, String>> fieldInfoList = databaseUtil.getTableFieldInfo(inputDBName, inputtableName, inputdburl, inputusername, inputpassword);
//			System.out.println("获取原表字段信息:" + new Gson().toJson(fieldInfoList));
            if (fieldInfoList == null) {
                log.info("1.获取原表字段信息失败");
                return false;
            }
            log.info("1.获取原表字段信息成功");
            // 2.生成创建表语句
            createTableSql = databaseUtil.getCreateTableSql(fieldInfoList, objList.get(0), outputtableName,
                    maskfields);
        }

//		System.out.println("建表SQL语句:" + createTableSql);
        if (createTableSql == null) {
            log.info("2.生成建表SQL语句失败");
            return false;
        }
        log.info("2.生成建表SQL语句成功,createTableSql:" + createTableSql);
        // 3.通过建表SQL语句、数据库连接信息创建表
        Boolean createMysqlTableResult = createTable(createTableSql, jdbc_driver, outputdburl, outputusername,
                outputpassword);
//		System.out.println("通过建表SQL语句创建表结果：" + createMysqlTableResult);
        if (!createMysqlTableResult) {
            log.info("3.通过建表SQL语句创建表失败");
            return false;
        }
        log.info("3.通过建表SQL语句创建表成功");
        // 4.生成批量插入语句,批量插入参数
        Map<String, Object> sqlAndParams = databaseUtil.getInsert2TableSqlAndPatams(objList, inputDBName, outputtableName);
        if (sqlAndParams == null) {
            log.info("4.生成批量插入语句,批量插入参数失败");
            return false;
        }
        log.info("4.生成批量插入语句,批量插入参数成功");
        String insert2MysqlTableSql = sqlAndParams.get("sql").toString();
        Object[] insert2MysqlTableParams = (Object[]) sqlAndParams.get("params");
//		System.out.println("批量插入SQL语句：" + insert2MysqlTableSql);
//		System.out.println("批量插入SQL数据：" + new Gson().toJson(insert2MysqlTableParams));
        // 5.通过批量插入语句、数据库连接信息批量插入数据
        Boolean insert2MysqlTableResult = executeUpdate(insert2MysqlTableSql, insert2MysqlTableParams, jdbc_driver,
                outputdburl, outputusername, outputpassword);
//		System.out.println("通过批量插入SQL语句批量插入数据结果：" + insert2MysqlTableResult);
        if (!insert2MysqlTableResult) {
            log.info("5.通过批量插入SQL语句批量插入数据失败");
            return false;
        }
        log.info("5.通过批量插入SQL语句批量插入数据成功");
        return true;
    }


    /**
     * 创建新表并批量插入数据到新表
     *
     * @param isCreateTable   是否创建表
     * @param inputDBName     输入库名
     * @param inputtableName  输入表名
     * @param inputdburl      输入数据库连接信息
     * @param inputusername   输入数据库用户名
     * @param inputpassword   输入数据库密码
     * @param objList         Map数据集合
     * @param outputtableName 输出新表名称
     * @param outputdburl     输出数据库连接信息
     * @param outputusername  输出数据库用户名
     * @param outputpassword  输出数据库密码
     * @param maskfields      加密字段
     * @param outsourcedbname 输出数据库库名
     * @return Boolean：true成功，false失败
     * <AUTHOR>
     * @date 2019年10月15日 下午3:36:16
     */
    public static Boolean insertData2NewTable(boolean isCreateTable, String inputDBName, String inputtableName, String inputdburl,
                                              String inputusername, String inputpassword,String in_dbtype, List<Map<String, String>> objList, String outputtableName,
                                              String outputdburl, String outputusername, String outputpassword, List<String> maskfields,
                                              String outsourcedbname, String out_dbtype, String watermarkField) {
        DatabaseUtil inputDatabaseUtil = null, outputDatabaseUtil = null;
        String inputJdbcDriver = null, outputJdbcDriver = null;
        Map<String, Object> inputDataBaseDriver = getDataBaseDriverByUrl(in_dbtype, inputDatabaseUtil, inputJdbcDriver);
        Map<String, Object> outoutDataBaseDriver = getDataBaseDriverByUrl(out_dbtype, outputDatabaseUtil, outputJdbcDriver);

        //数据源和目标源正常都有对应的util和driver，才可继续执行脱敏
        if (inputDataBaseDriver == null || outoutDataBaseDriver == null) {
            return false;
        }
        inputDatabaseUtil = (DatabaseUtil) inputDataBaseDriver.get("databaseUtil");
        inputJdbcDriver = inputDataBaseDriver.get("jdbcDriver").toString();
        outputDatabaseUtil = (DatabaseUtil) outoutDataBaseDriver.get("databaseUtil");
        outputJdbcDriver = outoutDataBaseDriver.get("jdbcDriver").toString();

        //1.获取数据源获取原表字段信息
        List<Map<String, String>> tableFieldInfo = inputDatabaseUtil.getTableFieldInfo(inputDBName, inputtableName, inputdburl, inputusername, inputpassword);

        if (isCreateTable) {
            String createTableSql = null;
            if (tableFieldInfo == null || tableFieldInfo.size() == 0) {
                log.info("1.获取原表字段信息失败");
                return false;
            }
            log.info("1.获取原表字段信息成功");
            if (Const.DB_MYSQL.equalsIgnoreCase(in_dbtype) && Const.DB_MYSQL.equalsIgnoreCase(out_dbtype)){
                String srcCreatetablesql = MysqlUtil.getSrcCreateTableSql(inputtableName, inputdburl, inputusername,
                        inputpassword);
                createTableSql = MysqlUtil.getCreateTableSql(srcCreatetablesql,tableFieldInfo, objList.get(0), outputtableName, outsourcedbname,maskfields, watermarkField);
            } else {
                createTableSql = outputDatabaseUtil.getCreateTableSql(tableFieldInfo, objList.get(0), outputtableName, maskfields, outsourcedbname, watermarkField);
            }

            if (createTableSql == null) {
                log.info("2.生成建表SQL语句失败");
                return false;
            }
            log.info("2.生成建表SQL语句成功,createTableSql:" + createTableSql);
            // 3.通过建表SQL语句、数据库连接信息创建表
            Boolean createMysqlTableResult = createTable(createTableSql, outputJdbcDriver, outputdburl, outputusername,
                    outputpassword);
            if (!createMysqlTableResult) {
                log.info("3.通过建表SQL语句创建表失败");
                return false;
            }
            log.info("3.通过建表SQL语句创建表成功");

        }


        Map<String, Object> sqlAndParams = null;
        int pageSize = 100;
        int end = pageSize;

        if (outputdburl.toLowerCase().contains("informix")){
            for (int i = 0; i < objList.size(); i += pageSize) {
                if (objList.size() < end) {
                    end = objList.size();
                }
                // 4.生成批量插入语句,批量插入参数
                sqlAndParams = InformixUtil.getInsertTableSqlAndPatams(i, end, objList, outsourcedbname, outputtableName);
                if (sqlAndParams == null) {
                    log.info("生成批量插入语句,批量插入参数失败,开始行：" + i + "，结束行：" + end);
                    return false;
                }
                log.info("生成批量插入语句,批量插入参数成功,开始行：" + i + "，结束行：" + end + "，当前批次共" + objList.size() + "行");

                List<String> sqlList = (List)sqlAndParams.get("sql");
                List<List<Object>> paramsList = (List) sqlAndParams.get("params");
                // 5.通过批量插入语句、数据库连接信息批量插入数据
                Boolean insert2MysqlTableResult = InformixUtil.executeUpdate(sqlList, paramsList, outputJdbcDriver, outputdburl, outputusername, outputpassword);
                if (!insert2MysqlTableResult) {
                    log.info("通过批量插入SQL语句批量插入数据失败");
                    return false;
                }
                end += pageSize;
            }
        } else {

            //TODO 将数字类型的数据，从字符串类型转换为数值型
            List<Map<String, Object>> alterFieldList = null;
            if (Const.DB_HIGHGO.equalsIgnoreCase(out_dbtype) || Const.DB_POSTGRESQL.equalsIgnoreCase(out_dbtype)){
                alterFieldList = new ArrayList<>();
                List<String> alterField = new ArrayList<>();
                for (Map<String, String> map : tableFieldInfo) {
                    String fieldEName = map.get("FieldEName");
                    String fieldType = map.get("FieldType");
                    if (!maskfields.contains(fieldEName) && fieldType.toLowerCase().contains("int")){
                        alterField.add(fieldEName);
                    }
                }

                if (alterField.size() > 0) {
                    for (Map<String, String> map : objList) {
                        Map<String, Object> objMap = new LinkedHashMap<>();
                        for (Map.Entry<String, String> entry : map.entrySet()) {
                            Object data = null;
                            if (alterField.contains(entry.getKey())) {
                                data = Long.valueOf(entry.getValue());
                            } else {
                                data = entry.getValue();
                            }
                            objMap.put(entry.getKey(), data);
                        }
                        alterFieldList.add(objMap);
                    }
                }
            }

            for (int i = 0; i < objList.size(); i += pageSize) {
                if (objList.size() < end) {
                    end = objList.size();
                }
                // 4.生成批量插入语句,批量插入参数
                if (Const.DB_HIGHGO.equalsIgnoreCase(out_dbtype) || Const.DB_POSTGRESQL.equalsIgnoreCase(out_dbtype)){
                    // TODO 瀚高、PG数据库，数值类型字段无法使用字符串数据写入，需要特殊处理
                    sqlAndParams = PostGreSQLUtil.getInsertTableSqlAndPatams(i, end, alterFieldList, outsourcedbname, outputtableName);
                } else {
                    sqlAndParams = outputDatabaseUtil.getInsert2TableSqlAndPatams(i, end, objList, outsourcedbname, outputtableName);
                }

                if (sqlAndParams == null) {
                    log.info("生成批量插入语句,批量插入参数失败,开始行：" + i + "，结束行：" + end);
                    return false;
                }
                log.info("生成批量插入语句,批量插入参数成功,开始行：" + i + "，结束行：" + end + "，当前批次共" + objList.size() + "行");
                String insert2MysqlTableSql = sqlAndParams.get("sql").toString();
                Object[] insert2MysqlTableParams = (Object[]) sqlAndParams.get("params");
                // 5.通过批量插入语句、数据库连接信息批量插入数据
                Boolean insert2MysqlTableResult = executeUpdate(insert2MysqlTableSql, insert2MysqlTableParams, outputJdbcDriver,
                        outputdburl, outputusername, outputpassword);
                if (!insert2MysqlTableResult) {
                    log.info("通过批量插入SQL语句批量插入数据失败");
                    return false;
                }
                end += pageSize;
            }
        }
        log.info("通过批量插入SQL语句批量插入数据成功");
        return true;
    }

    public static Boolean mongoDBDataNewTable(boolean isCreateTable,String out_dbname, String out_tabname, Integer dbTaskWriteDataCount,
                                              MongoClient outMongoClient, List<Map<String, String>> fieldDataList) throws Exception {
        Boolean insertDataOrNot = true;
        Boolean flag = false;
        if (isCreateTable) {
            //校验原集合是否存在
            Map<String, String> allDbTabMap = MongoDBUtil.getAllDbAndTabMap(outMongoClient, out_dbname);
            String tabName = null;
            for (String key : allDbTabMap.keySet()) {
                if (key.equalsIgnoreCase(out_dbname)) {
                    tabName = allDbTabMap.get(key);
                    break;
                }
            }

            // TODO MongoDB创建集合
            if (tabName != null && tabName.contains(out_tabname)) {
                log.info("输出源已存在该表,请先进行删除再重试");
                insertDataOrNot = false;
            } else {
                MongoDBUtil.createCollection(out_dbname, out_tabname, outMongoClient);
            }
        }

        if (insertDataOrNot) {
            try {

                Integer batchSize = dbTaskWriteDataCount;  // 每次处理的批次大小

                List<Document> documentList = new ArrayList<>();
                for (Map<String, String> fieldData : fieldDataList) {
                    Document document = new Document();
                    // 遍历 fieldData 中的每个键值对
                    for (Map.Entry<String, String> entry : fieldData.entrySet()) {
                        String fieldName = entry.getKey();
                        String fieldValue = entry.getValue();
                        document.append(fieldName, fieldValue);
                    }
                    documentList.add(document);
                }

                while (!documentList.isEmpty()) {
                    List<Document> batch = new ArrayList<>();
                    // 从 dataList 中取出 batchSize 个数据
                    for (int i = 0; i < batchSize && i < documentList.size(); i++) {
                        batch.add(documentList.get(i));
                    }
                    // 处理 batch 中的数据
                    MongoDBUtil.dataWriteCollection(out_dbname, out_tabname, outMongoClient, batch);
                    // 删除已处理的数据
                    documentList.subList(0, Math.min(batchSize, documentList.size())).clear();
                }
                flag = true;
            } catch (Exception e) {
                flag = false;
                e.printStackTrace();
            }
        }

        return flag;
    }

    public static Map<String,Object> getDataBaseDriverByUrl(String dbType, DatabaseUtil databaseUtil, String jdbcDriver) {
        Map<String,Object> returnMap = new HashMap();
        if (dbType.equalsIgnoreCase("oracle")) {
            databaseUtil = new OracleUtil();
            jdbcDriver = Const.DB_DRIVER_ORACLE;
        } else if (dbType.equalsIgnoreCase("mysql")) {
            databaseUtil = new MysqlUtil();
            jdbcDriver = Const.DB_DRIVER_MYSQL;
        } else if (dbType.equalsIgnoreCase("gbase")) {
            databaseUtil = new GbaseUtil();
            jdbcDriver = Const.DB_DRIVER_GBASE;
        } else if (dbType.equalsIgnoreCase("sqlserver")) {
            databaseUtil = new SQLServerUtil();
            jdbcDriver = Const.DB_DRIVER_SQLSERVER;
        } else if (dbType.equalsIgnoreCase("db2")) {
            databaseUtil = new DB2Util();
            jdbcDriver = Const.DB_DRIVER_DB2;
        } else if (dbType.equalsIgnoreCase("hive2")) {
            databaseUtil = new HiveUtil();
            jdbcDriver = Const.DB_DRIVER_HIVE2;
        } else if (dbType.equalsIgnoreCase(Const.DB_POSTGRESQL) || dbType.equalsIgnoreCase(Const.DB_GAUSS) || dbType.equalsIgnoreCase(Const.DB_GAUSSDB_OPENGAUSS) || dbType.equalsIgnoreCase(Const.DB_GAUSSDB_MYSQL)) {//高斯数据库默认使用PG数据源，驱动程序和PG一致
            databaseUtil = new PostGreSQLUtil();
            jdbcDriver = Const.DB_DRIVER_POSTGRESQLDB;
        } else if (dbType.equalsIgnoreCase("dm")) {
            databaseUtil = new DMUtil();
            jdbcDriver = Const.DB_DRIVER_DM;
        } else if (dbType.equalsIgnoreCase("informix")) {
            databaseUtil = new InformixUtil();
            jdbcDriver = Const.DB_DRIVER_INFORMIX;
        } else if (dbType.equalsIgnoreCase("clickhouse") || dbType.equalsIgnoreCase("bytehouse")) {
            databaseUtil = new ByteHouseUtil();
            jdbcDriver = Const.DB_DRIVER_BYTEHOUSE;
        } else if (dbType.equalsIgnoreCase(Const.DB_HIGHGO)) {
            databaseUtil = new HighGoUtil();
            jdbcDriver = Const.DB_DRIVER_HIGHGO;
        }
        if (databaseUtil == null || jdbcDriver == null) {
            return null;
        }
        returnMap.put("databaseUtil",databaseUtil);
        returnMap.put("jdbcDriver",jdbcDriver);
        return returnMap;
    }


    /**
     * 执行SQL语句(通过创建表SQL语句、数据库连接信息创建表)
     *
     * @param createsql 创建表SQL语句
     * @param jdbcName  数据库驱动
     * @param dburl     数据库连接信息
     * @param username  数据库用户名
     * @param password  数据库密码
     * @return Boolean：true成功，false失败
     * <AUTHOR>
     * @date 2019年10月15日10:07:52
     */
    public static Boolean createTable(String createsql, String jdbcName, String dburl, String username,
                                      String password) {
        Boolean result = false;
        Connection conn = null;
        Statement stmt = null;
        try {
            conn = getConn(jdbcName, dburl, username, password);// 打开连接
            stmt = conn.createStatement();// 执行创建表
            if (0 == stmt.executeUpdate(createsql)) {
                result = true;
            }
        } catch (Exception e) {
            log.info("createsql:"+createsql);
            e.printStackTrace();
            result = false;
        } finally {
            closeCon(null, stmt, conn);
        }
        return result;
    }

    /**
     * 数据库查询方法(执行批量INSERT)
     *
     * @param sql      SQL语句
     * @param ob       参数
     * @param jdbcName 数据库驱动
     * @param dburl    数据库连接信息
     * @param username 数据库用户名
     * @param password 数据库密码
     * @return ResultSet 结果集
     * <AUTHOR>
     * @date 2019年11月4日 下午2:04:10
     */
    public static ResultSet prepareStatement(String sql, Object[] ob, String jdbcName, String dburl, String username, String password) {
        Connection conn = null;
        PreparedStatement ps = null;
        try {
            conn = getConn(jdbcName, dburl, username, password);// 打开连接
            ps = conn.prepareStatement(sql);
            if (ps != null && ob != null) {
                for (int i = 0; i < ob.length; i++) {
                    ps.setObject((i+1), ob[i]);
                }
            }
            return ps.executeQuery();
        } catch (SQLException e1) {
            //e1.printStackTrace();
            log.info("："+e1.getMessage());
            return null;
        } finally {
            closeCon(null, ps, conn);
        }

    }


    /**
     * 数据库增删改方法
     *
     * @param sql      执行SQL语句
     * @param jdbcName 数据库驱动
     * @param dburl    数据库连接信息
     * @param username 数据库用户名
     * @param password 数据库密码
     * @return Boolean：true成功，false失败
     * <AUTHOR>
     * @date 2019年10月15日10:07:46
     */
    public static Boolean executeUpdate(String sql, String jdbcName, String dburl, String username, String password) {
        Boolean result = false;
        Connection conn = null;
        PreparedStatement pstmt = null;
        try {
            conn = getConn(jdbcName, dburl, username, password);// 打开连接
            pstmt = conn.prepareStatement(sql);
            result = true;
        } catch (Exception e) {
            log.error("Exception: {}, jdbcName: {}, url: {}", e.getMessage(), jdbcName, dburl);
            e.printStackTrace();
            result = false;
        } finally {
            closeCon(null, pstmt, conn);
        }
        return result;
    }


    /**
     * 数据库增删改方法(执行批量INSERT)
     *
     * @param insertsql 批量插入SQL语句
     * @param params    批量插入参数
     * @param jdbcName  数据库驱动
     * @param dburl     数据库连接信息
     * @param username  数据库用户名
     * @param password  数据库密码
     * @return Boolean：true成功，false失败
     * <AUTHOR>
     * @date 2019年10月15日10:07:46
     */
    public static Boolean executeUpdate(String insertsql, Object[] params, String jdbcName, String dburl,
                                        String username, String password) {
        Boolean result = false;
        Connection conn = null;
        PreparedStatement pstmt = null;
        String executeSql = "";
        try {
            conn = getConn(jdbcName, dburl, username, password);// 打开连接
            String[] sqlCount = insertsql.split(";");
            Integer placeholderIndex = 0;
            //分批执行sql
            for (String sql : sqlCount) {
                Integer placeholderCount = 0;
                //统计sql里的占位符数量
                String[] str = sql.split("");
                for (String s : str) {
                    if ("?".equals(s)){
                        placeholderCount ++;
                    }
                }
                pstmt = conn.prepareStatement(sql);
                //将占位符替换成实际数据
                for (int i = 0; i < placeholderCount; i++) { ;
                    Object data = params[placeholderIndex];
                    pstmt.setObject(i + 1, "".equals(data) ? null : data);
                    placeholderIndex ++;
                }
                executeSql = pstmt.toString();
                if (pstmt.executeUpdate() > 0) {
                    result = true;
                }
                pstmt.close();
            }

			/*pstmt = conn.prepareStatement(insertsql);
			for (int i = 0; i < params.length; i++) {
				pstmt.setObject(i + 1, params[i]);
			}
			if (pstmt.executeUpdate() > 0) {
				result = true;
			}*/
        } catch (Exception e) {
            try {
                // 截取出一个完整的insert SQl，便于错误排查
                int startIndex = executeSql.indexOf("VALUES (");
                String substring = executeSql.substring(startIndex, executeSql.length());
                int endIndex = substring.indexOf("),")+1;
                executeSql = executeSql.substring(0,(startIndex + endIndex));
                log.info("数据写入错误，executeSql：" + executeSql);
            }catch (Exception ex){}

            e.printStackTrace();
            result = false;
        } finally {
            closeCon(null, pstmt, conn);
        }
        return result;
    }


    /**
     * 根据数据库连接获取所有库表
     *
     * @param type    数据库类型
     * @param conn    数据库连接
     * @param dbnames 库名
     * @return {@link Map}<{@link String}, {@link String}>
     * @throws SQLException sqlexception异常
     * <AUTHOR>
     * @date 2020-4-22
     */
    public static Map<String, String> getAllDbTabMap(String type, Object conn, String dbnames) throws Exception {
        Map<String, String> dbTabMap = null;
        try {
            if (type.equalsIgnoreCase("mysql")) {
                dbTabMap = MysqlUtil.getAllDbAndTabMap((Connection) conn, dbnames);
            } else if (type.equalsIgnoreCase("oracle")) {
                dbTabMap = OracleUtil.getAllDbAndTabMap((Connection) conn, dbnames);
            } else if (type.equalsIgnoreCase("gbase")) {
                dbTabMap = GbaseUtil.getAllDbAndTabMap((Connection) conn, dbnames);
            } else if (type.equalsIgnoreCase("hive")) {
                dbTabMap = HiveUtil.getAllDbAndTabMap((Connection) conn, dbnames);
            } else if (type.equalsIgnoreCase("hbase")) {
                dbTabMap = HBaseUtil.getAllDbAndTabMap((org.apache.hadoop.hbase.client.Connection) conn, dbnames);
            } else if (type.equalsIgnoreCase("sqlserver")) {
                dbTabMap = SQLServerUtil.getAllDbAndTabMap((Connection) conn, dbnames);
            } else if (type.equalsIgnoreCase("dm")) {
                dbTabMap = DMUtil.getAllDbAndTabMap((Connection) conn, dbnames);
            } else if (type.equalsIgnoreCase("mariadb")) {
                dbTabMap = MariaDBUtil.getAllDbAndTabMap((Connection) conn, dbnames);
            } else if (type.equalsIgnoreCase(Const.DB_POSTGRESQL) || type.equalsIgnoreCase(Const.DB_GAUSS) || type.equalsIgnoreCase(Const.DB_GAUSSDB_OPENGAUSS) || type.equalsIgnoreCase(Const.DB_GAUSSDB_MYSQL)) {//高斯数据库默认使用PG数据源，驱动程序和PG一致
                dbTabMap = PostGreSQLUtil.getAllDbAndTabMap((Connection) conn, dbnames);
            } else if (type.equalsIgnoreCase(Const.DB_MONGODB)) {
                dbTabMap = MongoDBUtil.getAllDbAndTabMap((MongoClient) conn, dbnames);
            } else if (type.equalsIgnoreCase(Const.DB_BYTEHOSE)) {
                dbTabMap = ByteHouseUtil.getAllDbAndTabMap((Connection) conn, dbnames);
            } else if (type.equalsIgnoreCase(Const.DB_HIGHGO)) {
                dbTabMap = HighGoUtil.getAllDbAndTabMap((Connection) conn, dbnames);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("获取数据库:" + dbnames + "中所有库名表名出现异常");
            throw ex;
        }
        return dbTabMap;
    }

    /**
     * 查询表信息
     *
     * @param type      数据库类型
     * @param dbName    库名
     * @param tableName 表名
     * @param conn      数据库连接
     * @return List<Map < String, String>>：数据库表信息,tableName和tableCName
     * <AUTHOR>
     * @date 2020年6月11日09:42:56
     */
    public static Map<String, String> getTableInfoBySchema(String type, String dbName, String tableName, Object conn) throws Exception {
        Map<String, String> tableInfoMap = null;
        try {
            if (type.equalsIgnoreCase("mysql")) {
                tableInfoMap = MysqlUtil.getTableInfoBySchema(dbName, tableName, (Connection) conn);
            } else if (type.equalsIgnoreCase("oracle")) {
                tableInfoMap = OracleUtil.getTableInfoBySchema(dbName, tableName, (Connection) conn);
            } else if (type.equalsIgnoreCase("gbase")) {
                tableInfoMap = GbaseUtil.getTableInfoBySchema(dbName, tableName, (Connection) conn);
            } else if (type.equalsIgnoreCase("hive")) {
                tableInfoMap = HiveUtil.getTableInfoBySchema(dbName, tableName, (Connection) conn);
            } else if (type.equalsIgnoreCase("hbase")) {
                tableInfoMap = HBaseUtil.getTableInfoBySchema(dbName, tableName, (org.apache.hadoop.hbase.client.Connection) conn);
            } else if (type.equalsIgnoreCase("sqlserver")) {
                tableInfoMap = SQLServerUtil.getTableInfoBySchema(dbName, tableName, (Connection) conn);
            } else if (type.equalsIgnoreCase("dm")) {
                tableInfoMap = DMUtil.getTableInfoBySchema(dbName, tableName, (Connection) conn);
            } else if (type.equalsIgnoreCase("mariadb")) {
                tableInfoMap = MariaDBUtil.getTableInfoBySchema(dbName, tableName, (Connection) conn);
            } else if (type.equalsIgnoreCase(Const.DB_POSTGRESQL) || type.equalsIgnoreCase(Const.DB_GAUSS) || type.equalsIgnoreCase(Const.DB_GAUSSDB_OPENGAUSS) || type.equalsIgnoreCase(Const.DB_GAUSSDB_MYSQL)) {//高斯数据库默认使用PG数据源，驱动程序和PG一致
                tableInfoMap = PostGreSQLUtil.getTableInfoBySchema(dbName, tableName, (Connection) conn);
            } else if (type.equalsIgnoreCase(Const.DB_MONGODB)) {
                tableInfoMap = MongoDBUtil.getTableInfoBySchema(dbName, tableName, (MongoClient) conn);
            } else if (type.equalsIgnoreCase(Const.DB_BYTEHOSE)) {
                tableInfoMap = ByteHouseUtil.getTableInfoBySchema(dbName, tableName, (Connection) conn);
            } else if (type.equalsIgnoreCase(Const.DB_HIGHGO)) {
                tableInfoMap = HighGoUtil.getTableInfoBySchema(dbName, tableName, (Connection) conn);
            }
        } catch (Exception ex) {
            log.error("获取数据库:" + dbName + "中表信息出现异常");
            throw ex;
        }
        return tableInfoMap;
    }

    /**
     * 获取数据库表中所有字段名
     *
     * @param type    数据库类型
     * @param conn    数据库连接
     * @param dbname  库名
     * @param tabname 表名
     * @return {@link List}<{@link String}>
     * @throws SQLException sqlexception异常
     * <AUTHOR>
     * @date 2020-4-22
     */
    public static List<String> getTabFieldList(String type, Object conn, String dbname, String tabname) throws Exception {
        List<String> fieldsList = null;
        try {
            if (type.equalsIgnoreCase("mysql")) {
                fieldsList = MysqlUtil.getFieldNameList((Connection) conn, dbname, tabname);
            } else if (type.equalsIgnoreCase("oracle")) {
                fieldsList = OracleUtil.getFieldNameList((Connection) conn, dbname, tabname);
            } else if (type.equalsIgnoreCase("gbase")) {
                fieldsList = GbaseUtil.getFieldNameList((Connection) conn, dbname, tabname);
            } else if (type.equalsIgnoreCase("hive")) {
                fieldsList = HiveUtil.getFieldNameList((Connection) conn, dbname, tabname);
            } else if (type.equalsIgnoreCase("hbase")) {
                fieldsList = HBaseUtil.getFieldNameList((org.apache.hadoop.hbase.client.Connection) conn, dbname, tabname);
            } else if (type.toLowerCase().equals("sqlserver")) {
                fieldsList = SQLServerUtil.getFieldNameList((Connection) conn, dbname, tabname);
            } else if (type.toLowerCase().equals("dm")) {
                fieldsList = DMUtil.getFieldNameList((Connection) conn, dbname, tabname);
            } else if (type.toLowerCase().equals("mariadb")) {
                fieldsList = MariaDBUtil.getFieldNameList((Connection) conn, dbname, tabname);
            } else if (type.equalsIgnoreCase(Const.DB_POSTGRESQL) || type.equalsIgnoreCase(Const.DB_GAUSS) || type.equalsIgnoreCase(Const.DB_GAUSSDB_OPENGAUSS) || type.equalsIgnoreCase(Const.DB_GAUSSDB_MYSQL)) {//高斯数据库默认使用PG数据源，驱动程序和PG一致
                fieldsList = PostGreSQLUtil.getFieldNameList((Connection) conn, dbname, tabname);
            } else if (type.equalsIgnoreCase(Const.DB_MONGODB)) {
                fieldsList = MongoDBUtil.getFieldNameList((MongoClient) conn, dbname, tabname);
            } else if (type.equalsIgnoreCase(Const.DB_BYTEHOSE)) {
                fieldsList = ByteHouseUtil.getFieldNameList((Connection) conn, dbname, tabname);
            } else if (type.equalsIgnoreCase(Const.DB_HIGHGO)) {
                fieldsList = HighGoUtil.getFieldNameList((Connection) conn, dbname, tabname);
            }

        } catch (Exception ex) {
            log.error("获取数据库:" + dbname + "表:" + tabname + "中所有字段名出现异常");
            throw ex;
        }
        return fieldsList;
    }


    /**
     * 查询表字段信息
     *
     * @param type      类型
     * @param dbName    数据库名字
     * @param tableName 表名
     * @param conn      数据库连接
     * @param srcurl    数据库url
     * @return List<Map < String, String>>：数据库表字段信息,fieldName和fieldCName
     * @throws IOException ioexception
     * <AUTHOR>
     * @date 2020年6月11日09:42:08
     */
    public static List<Map<String, String>> getTableFieldInfoBySchema(String type, String dbName, String tableName, Object conn, String srcurl) throws Exception {
        List<Map<String, String>> tableFieldInfoList = null;
        try {
            if (type.equalsIgnoreCase("mysql")) {
                tableFieldInfoList = MysqlUtil.getTableFieldInfoBySchema(dbName, tableName, (Connection) conn);
            } else if (type.equalsIgnoreCase("oracle")) {
                tableFieldInfoList = OracleUtil.getTableFieldInfoBySchema(dbName, tableName, (Connection) conn);
            } else if (type.equalsIgnoreCase("gbase")) {
                tableFieldInfoList = GbaseUtil.getTableFieldInfoBySchema(dbName, tableName, (Connection) conn);
            } else if (type.equalsIgnoreCase("hive")) {
                tableFieldInfoList = HiveUtil.getTableFieldInfoBySchema(dbName, tableName, (Connection) conn);
            } else if (type.equalsIgnoreCase("hbase")) {
                tableFieldInfoList = HBaseUtil.getAllFieldName(dbName, tableName, (org.apache.hadoop.hbase.client.Connection) conn, srcurl);
            } else if (type.equalsIgnoreCase("sqlserver")) {
                tableFieldInfoList = SQLServerUtil.getTableFieldInfoBySchema(dbName, tableName, (Connection) conn);
            } else if (type.equalsIgnoreCase("dm")) {
                tableFieldInfoList = DMUtil.getTableFieldInfoBySchema(dbName, tableName, (Connection) conn);
            } else if (type.equalsIgnoreCase("mariadb")) {
                tableFieldInfoList = MariaDBUtil.getTableFieldInfoBySchema(dbName, tableName, (Connection) conn);
            } else if (type.equalsIgnoreCase(Const.DB_POSTGRESQL) || type.equalsIgnoreCase(Const.DB_GAUSS) || type.equalsIgnoreCase(Const.DB_GAUSSDB_OPENGAUSS) || type.equalsIgnoreCase(Const.DB_GAUSSDB_MYSQL)) {//高斯数据库默认使用PG数据源，驱动程序和PG一致
                tableFieldInfoList = PostGreSQLUtil.getTableFieldInfoBySchema(dbName, tableName, (Connection) conn);
            } else if (type.equalsIgnoreCase(Const.DB_MONGODB)) {
                tableFieldInfoList = MongoDBUtil.getTableFieldInfoBySchema(dbName, tableName, (MongoClient) conn);
            } else if (type.equalsIgnoreCase(Const.DB_BYTEHOSE)) {
                tableFieldInfoList = ByteHouseUtil.getTableFieldInfoBySchema(dbName, tableName, (Connection) conn);
            } else if (type.equalsIgnoreCase(Const.DB_HIGHGO)) {
                tableFieldInfoList = HighGoUtil.getTableFieldInfoBySchema(dbName, tableName, (Connection) conn);
            }
        } catch (Exception ex) {
            log.error("获取数据库:" + dbName + "中表字段信息出现异常");
            throw ex;
        }
        return tableFieldInfoList;
    }


    /**
     * 查询表字段信息
     *
     * @param type         数据库类型
     * @param dbName       库名
     * @param tableName    表名
     * @param conn         数据库连接
     * @param srcurl       数据库url
     * @param tabFieldList 表字段列表
     * @return List<Map < String, String>>：数据库表字段信息,fieldName和fieldCName
     * @throws IOException ioexception
     * <AUTHOR>
     * @date 2020年6月11日09:42:08
     */
    public static List<Map<String, String>> getTableFieldInfoBySchema(String type, String dbName, String tableName, Object conn, String srcurl, List<String> tabFieldList) throws Exception {
        List<Map<String, String>> tableFieldInfoList = null;
        try {
            if (type.equalsIgnoreCase("mysql")) {
                tableFieldInfoList = MysqlUtil.getTableFieldInfoBySchema(dbName, tableName, (Connection) conn);
            } else if (type.equalsIgnoreCase("oracle")) {
                tableFieldInfoList = OracleUtil.getTableFieldInfoBySchema(dbName, tableName, (Connection) conn);
            } else if (type.equalsIgnoreCase("gbase")) {
                tableFieldInfoList = GbaseUtil.getTableFieldInfoBySchema(dbName, tableName, (Connection) conn);
            } else if (type.equalsIgnoreCase("hive")) {
                tableFieldInfoList = HiveUtil.getTableFieldInfoBySchema(dbName, tableName, (Connection) conn);
            } else if (type.equalsIgnoreCase("hbase")) {
                tableFieldInfoList = HBaseUtil.getAllFieldName(dbName, tableName, (org.apache.hadoop.hbase.client.Connection) conn, srcurl);
            } else if (type.equalsIgnoreCase("sqlserver")) {
                tableFieldInfoList = SQLServerUtil.getTableFieldInfoBySchema(dbName, tableName, (Connection) conn);
            } else if (type.equalsIgnoreCase("dm")) {
                tableFieldInfoList = DMUtil.getTableFieldInfoBySchema(dbName, tableName, (Connection) conn, tabFieldList);
            } else if (type.equalsIgnoreCase("mariadb")) {
                tableFieldInfoList = MariaDBUtil.getTableFieldInfoBySchema(dbName, tableName, (Connection) conn);
            } else if (type.equalsIgnoreCase(Const.DB_POSTGRESQL) || type.equalsIgnoreCase(Const.DB_GAUSS) || type.equalsIgnoreCase(Const.DB_GAUSSDB_OPENGAUSS) || type.equalsIgnoreCase(Const.DB_GAUSSDB_MYSQL)) {//高斯数据库默认使用PG数据源，驱动程序和PG一致
                tableFieldInfoList = PostGreSQLUtil.getTableFieldInfoBySchema(dbName, tableName, (Connection) conn);
            } else if (type.equalsIgnoreCase(Const.DB_MONGODB)) {
                tableFieldInfoList = MongoDBUtil.getTableFieldInfoBySchema(dbName, tableName, (MongoClient) conn);
            } else if (type.equalsIgnoreCase(Const.DB_HIGHGO)) {
                tableFieldInfoList = HighGoUtil.getTableFieldInfoBySchema(dbName, tableName, (Connection) conn);
            }
        } catch (Exception ex) {
            log.error("获取数据库:" + dbName + "中表字段信息出现异常");
            throw ex;
        }
        return tableFieldInfoList;
    }

    /**
     * 根据数据库连接库名表名获取表中数据
     *
     * @param type    数据库类型
     * @param conn    数据库连接
     * @param dbname  库名
     * @param tabname 表名
     * @param lineNum 获取结果行数
     * @return {@link List}<{@link String[]}>
     * @throws SQLException sqlexception异常
     * <AUTHOR>
     * @date 2020-4-22
     */
    public static List<String[]> getTabDataList(String type, Object conn, String dbname, String tabname, Integer lineNum) throws Exception {
        List<String[]> tabDataList = null;
        try {
            if (type.equalsIgnoreCase("mysql")) {
                tabDataList = MysqlUtil.getTabDataList((Connection) conn, dbname, tabname, lineNum);
            } else if (type.equalsIgnoreCase("oracle")) {
                tabDataList = OracleUtil.getTabDataList((Connection) conn, dbname, tabname, lineNum);
            } else if (type.equalsIgnoreCase("gbase")) {
                tabDataList = GbaseUtil.getTabDataList((Connection) conn, dbname, tabname, lineNum);
            } else if (type.equalsIgnoreCase("hive")) {
                tabDataList = HiveUtil.getTabDataList((Connection) conn, dbname, tabname, lineNum);
            } else if (type.equalsIgnoreCase("hbase")) {
                tabDataList = HBaseUtil.getTabDataList((org.apache.hadoop.hbase.client.Connection) conn, dbname, tabname, lineNum);
            } else if (type.toLowerCase().equals("sqlserver")) {
                tabDataList = SQLServerUtil.getTabDataList((Connection) conn, dbname, tabname, lineNum);
            } else if (type.toLowerCase().equals("dm")) {
                tabDataList = DMUtil.getTabDataList((Connection) conn, dbname, tabname, lineNum);
            } else if (type.toLowerCase().equals("mariadb")) {
                tabDataList = MariaDBUtil.getTabDataList((Connection) conn, dbname, tabname, lineNum);
            } else if (type.equalsIgnoreCase(Const.DB_POSTGRESQL) || type.equalsIgnoreCase(Const.DB_GAUSS) || type.equalsIgnoreCase(Const.DB_GAUSSDB_OPENGAUSS) || type.equalsIgnoreCase(Const.DB_GAUSSDB_MYSQL)) {//高斯数据库默认使用PG数据源，驱动程序和PG一致
                tabDataList = PostGreSQLUtil.getTabDataList((Connection) conn, dbname, tabname, lineNum);
            } else if (type.equalsIgnoreCase(Const.DB_MONGODB)) {
                tabDataList = MongoDBUtil.getTabDataList((MongoClient) conn, dbname, tabname, lineNum);
            } else if (type.equalsIgnoreCase(Const.DB_BYTEHOSE)) {
                tabDataList = ByteHouseUtil.getTabDataList((Connection) conn, dbname, tabname, lineNum);
            } else if (type.equalsIgnoreCase(Const.DB_HIGHGO)) {
                tabDataList = HighGoUtil.getTabDataList((Connection) conn, dbname, tabname, lineNum);
            }

        } catch (Exception ex) {
            log.error("获取表中数据出现异常");
            throw ex;
        }
        return tabDataList;
    }

    /**
     * 根据数据库连接库名表名字段名获取表中字段数据
     *
     * @param type    数据库类型
     * @param conn    数据库连接
     * @param dbname  库名
     * @param tabname 表名
     * @param field   字段名
     * @param lineNum 获取结果集行数
     * @return {@link List}<{@link String[]}>
     * @throws SQLException sqlexception异常
     * <AUTHOR>
     * @date 2021-03-12
     */
    public static List<String[]> getFieldDataList(String type, Object conn, String dbname, String tabname, String field, Integer lineNum) throws Exception {
        List<String[]> tabDataList = null;
        try {
            if (type.equalsIgnoreCase("mysql")) {
                tabDataList = MysqlUtil.getFieldDataList((Connection) conn, dbname, tabname, field, lineNum);
            } else if (type.equalsIgnoreCase("oracle")) {
                tabDataList = OracleUtil.getFieldDataList((Connection) conn, dbname, tabname, field, lineNum);
            } else if (type.equalsIgnoreCase("gbase")) {
                tabDataList = GbaseUtil.getFieldDataList((Connection) conn, dbname, tabname, field, lineNum);
            } else if (type.equalsIgnoreCase("hive")) {
                tabDataList = HiveUtil.getFieldDataList((Connection) conn, dbname, tabname, field, lineNum);
            } else if (type.toLowerCase().equals("hbase")) {
                tabDataList = HBaseUtil.getFieldDataList((org.apache.hadoop.hbase.client.Connection) conn, dbname, tabname, field, lineNum);
            } else if (type.toLowerCase().equals("sqlserver")) {
                tabDataList = SQLServerUtil.getFieldDataList((Connection) conn, dbname, tabname, field, lineNum);
            } else if (type.toLowerCase().equals("dm")) {
                tabDataList = DMUtil.getTabDataList((Connection) conn, dbname, tabname, lineNum);
            } else if (type.toLowerCase().equals("mariadb")) {
                tabDataList = MariaDBUtil.getTabDataList((Connection) conn, dbname, tabname, lineNum);
            } else if (type.equalsIgnoreCase(Const.DB_POSTGRESQL) || type.equalsIgnoreCase(Const.DB_GAUSS) || type.equalsIgnoreCase(Const.DB_GAUSSDB_OPENGAUSS) || type.equalsIgnoreCase(Const.DB_GAUSSDB_MYSQL)) {//高斯数据库默认使用PG数据源，驱动程序和PG一致
                tabDataList = PostGreSQLUtil.getTabDataList((Connection) conn, dbname, tabname, lineNum);
            } else if (type.equalsIgnoreCase(Const.DB_MONGODB)) {
                tabDataList = MongoDBUtil.getTabDataList((MongoClient)conn,dbname,tabname,lineNum);
            } else if (type.equalsIgnoreCase(Const.DB_BYTEHOSE)) {
                tabDataList = ByteHouseUtil.getTabDataList((Connection)conn,dbname,tabname,lineNum);
            } else if (type.equalsIgnoreCase(Const.DB_HIGHGO)) {
                tabDataList = HighGoUtil.getTabDataList((Connection)conn,dbname,tabname,lineNum);
            }

        } catch (Exception ex) {
            log.error("获取表中数据出现异常");
            throw ex;
        }
        return tabDataList;
    }

    /**
     * 根据数据库连接库名表名获取表中数据总条数
     *
     * @param type    数据库类型
     * @param conn    数据库连接
     * @param dbname  库名
     * @param tabname 表名
     * @return int 数据总条数
     * @throws SQLException sqlexception异常
     * <AUTHOR>
     * @date 2020-4-22
     */
    public static int getTabDataCount(String type, Object conn, String dbname, String tabname) throws SQLException {
        int count = 0;
        try {
            if (type.equalsIgnoreCase("mysql")) {
                count = MysqlUtil.getTabDataCount((Connection) conn, dbname, tabname);
            } else if (type.equalsIgnoreCase("oracle")) {
                count = OracleUtil.getTabDataCount((Connection) conn, dbname, tabname);
            } else if (type.equalsIgnoreCase("gbase")) {
                count = GbaseUtil.getTabDataCount((Connection) conn, dbname, tabname);
            } else if (type.equalsIgnoreCase("hive")) {
                count = HiveUtil.getTabDataCount((Connection) conn, dbname, tabname);
            } else if (type.toLowerCase().equals("hbase")) {
                count = HBaseUtil.getTabDataCount((org.apache.hadoop.hbase.client.Connection) conn, dbname, tabname);
            } else if (type.toLowerCase().equals("sqlserver")) {
                count = SQLServerUtil.getTabDataCount((Connection) conn, dbname, tabname);
            } else if (type.toLowerCase().equals("mariadb")) {
                count = MariaDBUtil.getTabDataCount((Connection) conn, dbname, tabname);
            } else if (type.equalsIgnoreCase(Const.DB_POSTGRESQL) || type.equalsIgnoreCase(Const.DB_GAUSS) || type.equalsIgnoreCase(Const.DB_GAUSSDB_OPENGAUSS) || type.equalsIgnoreCase(Const.DB_GAUSSDB_MYSQL)) {//高斯数据库默认使用PG数据源，驱动程序和PG一致
                count = PostGreSQLUtil.getTabDataCount((Connection) conn, dbname, tabname);
            } else if (type.equalsIgnoreCase(Const.DB_BYTEHOSE)) {
                count = ByteHouseUtil.getTabDataCount((Connection) conn, dbname, tabname);
            } else if (type.equalsIgnoreCase(Const.DB_HIGHGO)) {
                count = HighGoUtil.getTabDataCount((Connection) conn, dbname, tabname);
            }
        } catch (Exception ex) {
            log.error("获取表中数据总条数出现异常");
            throw ex;
        }
        return count;
    }

    public static boolean createTableSql(String inputdburl, String inputtableName,String in_dbtype,
                                         String inputusername, String inputpassword, List<Map<String, String>> objList,
                                         String outputtableName, String outsourcedbname, String outputdburl,
                                         String outputusername, String outputpassword, List<String> maskfields){

        DatabaseUtil databaseUtil = null;
        String jdbc_driver = null;
        Map<String, Object> inputDataBaseDriver= getDataBaseDriverByUrl(in_dbtype,databaseUtil, jdbc_driver);

        if (inputDataBaseDriver == null) {
            return false;
        }
        databaseUtil = (DatabaseUtil) inputDataBaseDriver.get("databaseUtil");
        jdbc_driver = inputDataBaseDriver.get("jdbcDriver").toString();

        String createTableSql = null;

        if (inputdburl.contains("mysql") || inputdburl.contains("MYSQL")) {
            String srcCreatetablesql = MysqlUtil.getSrcCreateTableSql(inputtableName, inputdburl, inputusername,
                    inputpassword);
            List<Map<String, String>> fieldInfoList = databaseUtil.getTableFieldInfo(null,inputtableName, inputdburl,
                    inputusername, inputpassword);
            if ("".equals(srcCreatetablesql)) {
                log.info("1.获取原表字段信息失败");
                return false;
            }
            log.info("1.获取原表字段信息成功");
            createTableSql = MysqlUtil.getCreateTableSql(srcCreatetablesql, fieldInfoList, objList.get(0),
                    outputtableName,outsourcedbname, maskfields, null);

        } else {
            // 1.读取源表结构
            List<Map<String, String>> fieldInfoList = databaseUtil.getTableFieldInfo(null,inputtableName, inputdburl,
                    inputusername, inputpassword);
            // System.out.println("获取原表字段信息:" + new
            // Gson().toJson(fieldInfoList));
            if (fieldInfoList == null) {
                log.info("1.获取原表字段信息失败");
                return false;
            }
            log.info("1.获取原表字段信息成功");
            // 2.生成创建表语句
            createTableSql = databaseUtil.getCreateTableSql(fieldInfoList, objList.get(0), outputtableName,
                    maskfields, outsourcedbname,null);
        }
        log.info("建表SQL语句:" + createTableSql);
        if (createTableSql == null) {
            log.info("2.生成建表SQL语句失败");
            return false;
        }
        log.info("2.生成建表SQL语句成功");
        // 3.通过建表SQL语句、数据库连接信息创建表
        Boolean createMysqlTableResult = createTable(createTableSql, jdbc_driver, outputdburl, outputusername,
                outputpassword);
        if (!createMysqlTableResult) {
            log.info("3.通过建表SQL语句创建表失败");
            return false;
        }
        log.info("3.通过建表SQL语句创建表成功");
        return true;
    }

    /**
     * 查询表字段信息
     *
     * @param dbName    库名
     * @param tableName 表名
     * @param dburl     数据库连接信息
     * @param username  数据库用户名
     * @param password  数据库密码
     * @return List<Map < String, String>> 数据库字段信息
     * <AUTHOR>
     * @date 2019年10月12日 下午3:15:07
     */
    protected abstract List<Map<String, String>> getTableFieldInfo(String dbName, String tableName, String dburl, String username,
                                                                   String password);


    /**
     * 通过Map数据集合生成批量插入SQL语句及插入语句参数（占位符形式）
     *
     * @param objList   Map数据库集合
     * @param dbName    库名
     * @param tableName 表名
     * @return Map<String, Object>：key为“sql”是批量插入语句，key为“params”是插入语句参数
     * <AUTHOR>
     * @date 2019年10月15日10:21:52
     */
    protected abstract Map<String, Object> getInsert2TableSqlAndPatams(List<Map<String, String>> objList, String dbName, String tableName);

    /**
     * 通过Map数据集合生成批量插入SQL语句及插入语句参数（占位符形式）
     *
     * @param start     批量插入开始位
     * @param end       批量插入结束位
     * @param objList   Map数据库集合
     * @param dbname    库名
     * @param tableName 表名
     * @return Map<String, Object>：key为“sql”是批量插入语句，key为“params”是插入语句参数
     * <AUTHOR>
     * @date 2019年10月15日10:21:52
     */
    public abstract Map<String, Object> getInsert2TableSqlAndPatams(int start, int end, List<Map<String, String>> objList, String dbname,
                                                                    String tableName);


    /**
     * 通过Map数据集合生成批量插入SQL语句及插入语句参数（占位符形式）
     *
     * @param start      批量插入开始位
     * @param end        批量插入结束位
     * @param objList    Map数据库集合
     * @param dbname     dbname
     * @param tableName  表名
     * @param fieldnames 字段名
     * @return Map<String, Object> key为“sql”是批量插入语句，key为“params”是插入语句参数
     * <AUTHOR>
     * @date 2020年11月13日10:21:52
     */
    public abstract Map<String, Object> getInsert2TableSqlAndPatams(int start, int end, List<Map<String, String>> objList, String dbname,
                                                                    String tableName, String fieldnames);


    /**
     * 通过Map生成创建表SQL语句，自动检测字段名及类型
     *
     * @param fieldInfoList 字段信息列表
     * @param obj           Map对象
     * @param tableName     表名
     * @param maskfields    脱敏字段
     * @return String：生成的创建表SQL语句
     * <AUTHOR>
     * @date 2019年10月11日 下午3:10:18
     */
    protected abstract String getCreateTableSql(List<Map<String, String>> fieldInfoList, Map<String, String> obj,
                                                String tableName, List<String> maskfields);


    /**
     * 通过Map生成创建表SQL语句，自动检测字段名及类型
     *
     * @param fieldInfoList 字段信息列表
     * @param obj           Map对象
     * @param tableName     表名
     * @param maskfields    脱敏字段
     * @param dbname        库名
     * @return String：生成的创建表SQL语句
     * <AUTHOR>
     * @date 2019年10月11日 下午3:10:18
     */
    protected abstract String getCreateTableSql(List<Map<String, String>> fieldInfoList, Map<String, String> obj,
                                                String tableName, List<String> maskfields, String dbname, String watermarkField);


}
