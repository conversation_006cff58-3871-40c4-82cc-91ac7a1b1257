package com.wzsec.webproxy.controller;

import com.wzsec.webproxy.service.WebProxyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * Web代理控制器
 * 处理所有代理请求
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Slf4j
@RestController
public class WebProxyController {

    @Autowired
    private WebProxyService webProxyService;

    /**
     * 处理所有HTTP请求的代理转发
     * 支持GET、POST、PUT、DELETE、PATCH、HEAD、OPTIONS等所有HTTP方法
     *
     * @param request  HTTP请求
     * @param response HTTP响应
     * @param body     请求体（可选）
     * @return 代理响应
     */
    @RequestMapping(value = "/**", 
                   method = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, 
                            RequestMethod.DELETE, RequestMethod.PATCH, RequestMethod.HEAD, 
                            RequestMethod.OPTIONS, RequestMethod.TRACE})
    public ResponseEntity<?> proxyRequest(HttpServletRequest request,
                                        HttpServletResponse response,
                                        @RequestBody(required = false) byte[] body) {
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 记录请求信息
            if (log.isDebugEnabled()) {
                log.debug("代理请求: {} {} from {}", 
                         request.getMethod(), 
                         request.getRequestURI(), 
                         getClientIpAddress(request));
            }
            
            // 处理代理请求
            ResponseEntity<?> result = webProxyService.handleProxyRequest(request, response, body);
            
            // 记录处理时间
            long processTime = System.currentTimeMillis() - startTime;
            if (log.isDebugEnabled()) {
                log.debug("代理请求处理完成，耗时: {}ms, 状态码: {}", 
                         processTime, 
                         result.getStatusCodeValue());
            }
            
            return result;
            
        } catch (Exception e) {
            long processTime = System.currentTimeMillis() - startTime;
            log.error("代理请求处理失败，耗时: {}ms, 请求: {} {}, 错误: {}", 
                     processTime, 
                     request.getMethod(), 
                     request.getRequestURI(), 
                     e.getMessage(), e);
            
            // 返回错误响应
            return ResponseEntity.status(500)
                                .body("代理服务器内部错误: " + e.getMessage());
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
