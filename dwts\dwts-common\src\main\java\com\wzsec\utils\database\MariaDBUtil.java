package com.wzsec.utils.database;

import com.wzsec.utils.Const;
import lombok.extern.slf4j.Slf4j;

import java.sql.*;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * MariaDB工具类
 */
@Slf4j
public class MariaDBUtil extends DatabaseUtil {

    private static String JDBC_DRIVER = "org.mariadb.jdbc.Driver";

    /**
     * 查询表字段信息
     *
     * @param tableName 表名
     * @param dburl     数据库连接信息
     * @param username  数据库用户名
     * @param password  数据库密码
     * @param dbName    数据库名
     * @return List<Map < String, String>>：数据库字段信息
     */
    @Override
    protected List<Map<String, String>> getTableFieldInfo(String dbName, String tableName, String dburl, String username,
                                                          String password) {
        List<Map<String, String>> fieldInfoList = null;
        Connection conn = null;
        Statement stmt = null;
        try {
            conn = getConn(JDBC_DRIVER, dburl, username, password);// 打开连接
            stmt = conn.createStatement();// 执行创建表
            ResultSet rs = stmt.executeQuery("SHOW FULL COLUMNS FROM `" + tableName + "`;");
            if (rs != null) {
                fieldInfoList = new ArrayList<>();
                while (rs.next()) {
                    Map<String, String> fieldInfoMap = new HashMap<>();
                    fieldInfoMap.put("Field", rs.getString("Field"));
                    fieldInfoMap.put("Type", rs.getString("Type"));
                    fieldInfoMap.put("Null", rs.getString("Null"));
                    fieldInfoMap.put("Key", rs.getString("Key"));
                    fieldInfoMap.put("Default", rs.getString("Default"));
                    fieldInfoMap.put("Extra", rs.getString("Extra"));
                    fieldInfoMap.put("Privileges", rs.getString("Privileges"));
                    fieldInfoMap.put("Comment", rs.getString("Comment"));
                    fieldInfoList.add(fieldInfoMap);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        closeCon(null, stmt, conn);
        return fieldInfoList;
    }

    /**
     * 通过Map数据集合生成批量插入SQL语句及插入语句参数（占位符形式）
     *
     * @param start     开始
     * @param end       结束
     * @param objList   数据列表
     * @param dbname    库名
     * @param tableName 表名
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    @Override
    public Map<String, Object> getInsert2TableSqlAndPatams(int start, int end, List<Map<String, String>> objList, String dbname, String tableName) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO `").append(tableName).append("` (");
            for (String column : fields) {
                sb.append("`").append(column).append("`, ");
            }
            String sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sb = new StringBuilder(sql);
            sb.append(") VALUES ");
            for (int i = start; i < end; i++) {
                Map<String, String> map = objList.get(i);
                sb.append("(");
                for (String key : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?, ");
                    params.add(map.get(key));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append("), ");
            }
            sql = sb.toString();
            lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            // sql += ";";
            sqlAndParams = new HashMap<>();
            sqlAndParams.put("sql", sql);
            sqlAndParams.put("params", params.toArray());
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }


    /**
     * 通过Map生成创建表SQL语句，自动检测字段名及类型
     *
     * @param obj           Map对象
     * @param tableName     表名
     * @param fieldInfoList 字段信息列表
     * @param maskfields    掩码字段
     * @param dbname        库名
     * @return String 生成的SQL语句
     */
    @Override
    protected String getCreateTableSql(List<Map<String, String>> fieldInfoList, Map<String, String> obj,
                                       String tableName, List<String> maskfields, String dbname, String watermarkField) {
        String sql = null;
        try {
            StringBuilder sb = new StringBuilder();
            // sb.append("\r\nDROP TABLE IF EXISTS
            // ").append("`").append(tableName).append("`").append(";\r\n");//删除表语句
            sb.append("CREATE TABLE `").append(tableName).append("` (\r\n");
            // boolean firstId = true;
            for (Map<String, String> fieldInfo : fieldInfoList) {
                if (!obj.keySet().contains(fieldInfo.get("Field"))) {// 跳过没有抽取的列
                    continue;
                }
                sb.append("`").append(fieldInfo.get("Field")).append("`");// 字段名
                if (maskfields != null && maskfields.contains(fieldInfo.get("Field"))) {// 脱敏的字段类型更改为varchar
                    sb.append(" varchar(255)");// 类型
                } else {
                    sb.append(" ").append(fieldInfo.get("Type"));// 类型
                }
                if ("NO".equalsIgnoreCase(fieldInfo.get("Null"))) {// 判断非空
                    sb.append(" NOT NULL");
                }
                if ("auto_increment".equalsIgnoreCase(fieldInfo.get("Extra"))) {// 判断非空
                    sb.append(" AUTO_INCREMENT");// 自增
                } else {
                    if (fieldInfo.get("Default") != null) {
                        sb.append(" DEFAULT '").append(fieldInfo.get("Default")).append("'");// 默认值
                    } else {
                        sb.append(" DEFAULT NULL");
                    }
                }
                if ("PRI".equalsIgnoreCase(fieldInfo.get("Key"))) {
                    sb.append(" PRIMARY KEY");// 主键
                }
                if (fieldInfo.get("Comment") != null && !"".equals(fieldInfo.get("Comment"))) {
                    sb.append(" COMMENT '").append(fieldInfo.get("Comment")).append("'");
                }
                sb.append(",\n");
            }
            sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sql = sql + "\r)ENGINE=InnoDB DEFAULT CHARSET= utf8;\r\n";
        } catch (Exception e) {
            e.printStackTrace();
            sql = null;
        }
        return sql;
    }


    /**
     * 拼接建表语句
     *
     * @param srcsql        srcsql
     * @param fieldInfoList 字段信息列表
     * @param obj           obj
     * @param tableName     表名
     * @param maskfields    maskfields
     * @return {@link String}
     */
    public static String getCreateTableSql(String srcsql, List<Map<String, String>> fieldInfoList, Map<String, String> obj,
                                           String tableName, List<String> maskfields) {
        Set<String> keySet = obj.keySet();
        StringBuffer sb = new StringBuffer();
        StringBuffer end_sb = new StringBuffer();
        String end_sql = srcsql.substring(srcsql.lastIndexOf(")"), srcsql.length());
//		String middle_sql = srcsql.substring(srcsql.indexOf("(")+1, srcsql.lastIndexOf(")"));
//		String[] srcfilemiddlesql = middle_sql.split(",");
        String middle_sql = srcsql.substring(srcsql.indexOf("(") + 1, srcsql.lastIndexOf(")")).trim();
        String[] srcfilemiddlesql = middle_sql.split("\\n");
        HashMap<String, String> srcfieldsql = new HashMap<String, String>();
        for (String s : srcfilemiddlesql) {
            String str = "";
            if (s.endsWith(",")) {
                str = s.substring(0, s.length() - 1).trim();
            } else {
                str = s.trim();
            }
            String fieldname = str.substring(1, str.lastIndexOf("`"));
            if (str.startsWith("`")) {
                srcfieldsql.put(fieldname, str);
            } else {
                for (String extractfieldnamestr : keySet) {
                    if (str.contains(extractfieldnamestr)) {
                        end_sb.append(str).append(",\r\n");
                    }

                }
            }
        }
        sb.append("CREATE TABLE `").append(tableName).append("` (\r\n");
        for (Map<String, String> fieldInfo : fieldInfoList) {
            String field = fieldInfo.get("Field");
            if (!keySet.contains(field)) {// 跳过没有抽取的列
                continue;
            }
            String fieldsql = srcfieldsql.get(field);
            if (maskfields != null && maskfields.contains(field) && fieldsql.contains("varchar")) {// 脱敏的字段类型更改为varchar
                int post = appearNumber(fieldsql, " ", 2);
                String str = fieldsql.substring(post, fieldsql.length());
                sb.append("`" + field + "`");
                sb.append(" varchar(255) ");// 类型
                int autoIndex = str.indexOf("AUTO_INCREMENT");
                if (autoIndex >= 0) {
                    str = str.replace("AUTO_INCREMENT", "");
                }
                sb.append(str).append(",\r\n");
            } else {
                sb.append(fieldsql).append(",\r\n");
            }

        }
        sb.append(end_sb.toString());
        int lastIndex = sb.lastIndexOf(",");// 去掉最后一个逗号
        int endAutoIndex = end_sql.indexOf("AUTO_INCREMENT");
        if (endAutoIndex >= 0) {
            String autoSql = end_sql.substring(endAutoIndex);
            autoSql = autoSql.substring(autoSql.indexOf("AUTO_INCREMENT"), autoSql.indexOf(" "));
            end_sql = end_sql.replace(autoSql, "");
        }
        return sb.substring(0, lastIndex) + end_sql;
    }


    /**
     * 获取字符串出现次数的位置
     *
     * @param fieldsql fieldsql
     * @param s        年代
     * @param i        我
     * @return int
     */
    private static int appearNumber(String fieldsql, String s, int i) {
        Pattern pattern = Pattern.compile(s);
        Matcher findMatcher = pattern.matcher(fieldsql);
        int number = 0;
        while (findMatcher.find()) {
            number++;
            if (number == i) {//当“i”次出现时停止
                break;
            }
        }
        return findMatcher.start();
    }

    /**
     * 获取原有建表语句
     *
     * @param tableName 表名
     * @param dburl     dburl
     * @param username  用户名
     * @param password  密码
     * @return {@link String}
     */
    public static String getSrcCreateTableSql(String tableName, String dburl, String username, String password) {
        String srccreatetablesql = null;
        Connection conn = null;
        Statement stmt = null;
        try {
            conn = getConn(JDBC_DRIVER, dburl, username, password);// 打开连接
            stmt = conn.createStatement();// 执行创建表
            ResultSet rs = stmt.executeQuery("show create table `" + tableName + "`;");
            if (rs != null) {
                while (rs.next()) {
                    srccreatetablesql = rs.getString("Create Table");
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        closeCon(null, stmt, conn);
        return srccreatetablesql;

    }


    /**
     * 获取数据库中所有的库名表名
     *
     * @param conn    连接
     * @param dbnames 库名
     * @return {@link Map}<{@link String}, {@link String}>
     * @throws SQLException sqlexception异常
     */
    public static Map<String, String> getAllDbAndTabMap(Connection conn, String dbnames) throws SQLException {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Map<String, String> dbTabMap = null;
        try {
            String strSQL = "select `table_schema`,`table_name` from `information_schema`.tables ";
            if (dbnames != null && !"".equals(dbnames)) {
                strSQL += " where `table_schema` in ('" + dbnames + "') ";
            }
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            dbTabMap = new TreeMap<String, String>();
            while (rs.next()) {
                String dbName = rs.getString(1);
                String table_name = rs.getString(2);
                if (!"sys".equals(dbName) && !"information_schema".equals(dbName) && !"performance_schema".equals(dbName)) {
                    if (dbTabMap.containsKey(dbName)) {
                        dbTabMap.put(dbName, dbTabMap.get(dbName) + "," + table_name);
                    } else {
                        dbTabMap.put(dbName, table_name);
                    }
                }

            }
        } catch (Exception ex) {
            log.error("获取数据库中所有的库名表名出现异常 :{}", ex.getMessage());
        } finally {
            closeCon(rs, stmt, null);
        }
        return dbTabMap;
    }

    /**
     * 获取数据库中所有的库名表名
     *
     * @param dburl    数据库URL
     * @param username 用户名
     * @param password 密码
     * @param dbnames  库名
     * @return {@link Map}<{@link String}, {@link String}>
     */
    public static Map<String, String> getAllDbAndTabMap(String dburl, String username, String password, String dbnames) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Map<String, String> dbTabMap = null;
        try {
            String strSQL = "select `table_schema`,`table_name` from `information_schema`.tables ";
            if (dbnames != null && !"".equals(dbnames)) {
                strSQL += " where `table_schema` in (" + dbnames + ") ";
            }
            conn = getConn(JDBC_DRIVER, dburl, username, password);// 打开连接
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            dbTabMap = new TreeMap<String, String>();
            while (rs.next()) {
                String dbName = rs.getString(1);
                String table_name = rs.getString(2);
                if (!"sys".equals(dbName) && !"mysql".equals(dbName) && !"information_schema".equals(dbName) && !"performance_schema".equals(dbName)) {
                    if (dbTabMap.containsKey(dbName)) {
                        dbTabMap.put(dbName, dbTabMap.get(dbName) + "," + table_name);
                    } else {
                        dbTabMap.put(dbName, table_name);
                    }
                }

            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
        } finally {
            closeCon(rs, stmt, conn);
        }
        return dbTabMap;
    }


    /**
     * 获取字段名称列表
     *
     * @param conn    连接
     * @param dbname  库名
     * @param tabname 表名
     * @return {@link List}<{@link String}>
     * @throws SQLException sqlexception异常
     */
    public static List<String[]> getTabDataList(Connection conn, String dbname, String tabname, Integer lineNum) throws SQLException {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<String[]> tabDataList = new ArrayList<String[]>();
        try {
            String strSQL = "select * from " + tabname;
            if (lineNum != null && lineNum != 0)
                strSQL += " order by rand() LIMIT " + lineNum;
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            ResultSetMetaData md = rs.getMetaData(); //获得结果集结构信息,元数据
            int columnCount = md.getColumnCount();   //获得列数
            while (rs.next()) {
                String[] row = new String[columnCount];
                for (int i = 0; i < columnCount; i++) {
                    row[i] = rs.getString(i + 1) == null ? "" : rs.getString(i + 1);
                }
                tabDataList.add(row);
            }
        } catch (Exception ex) {
            log.error("获取数据库中所有的库名表名出现异常: {}", ex.getMessage());
        } finally {
            closeCon(rs, stmt, null);
        }
        return tabDataList;
    }

    /**
     * 获取数据库表中前100条数据
     *
     * @param dburl    dburl
     * @param username 用户名
     * @param password 密码
     * @param dbname   dbname
     * @param tabname  tabname
     * @return {@link List}<{@link String}>
     */
    public static List<String> getTabDataList(String dburl, String username, String password, String dbname, String tabname) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<String> tabDataList = new ArrayList<String>();
        try {
            String strSQL = "select * from " + dbname + "." + tabname + " order by rand() LIMIT 100 ";
            conn = getConn(JDBC_DRIVER, dburl, username, password);// 打开连接
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            ResultSetMetaData md = rs.getMetaData(); //获得结果集结构信息,元数据
            int columnCount = md.getColumnCount();   //获得列数
            while (rs.next()) {
                StringBuffer sub = new StringBuffer();
                for (int i = 1; i <= columnCount; i++) {
                    if (i > 1) {
                        sub.append(Const.DB_TAB_DATA_JOIN);
                    }
                    sub.append(rs.getObject(i));
                }
                tabDataList.add(sub.toString());
            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
        } finally {
            closeCon(rs, stmt, conn);
        }
        return tabDataList;
    }


    /**
     * 获取字段名称列表
     *
     * @param conn    连接
     * @param dbname  库名
     * @param tabname 表名
     * @return {@link List}<{@link String}>
     * @throws SQLException sqlexception异常
     */
    public static List<String> getFieldNameList(Connection conn, String dbname, String tabname) throws SQLException {
        Statement stmt = null;
        ResultSet rs = null;
        List<String> list = new ArrayList<String>();
        try {
            String strSQL = "select COLUMN_NAME from  INFORMATION_SCHEMA.Columns where table_name='" + tabname + "' and table_schema='" + dbname + "' ORDER BY  ORDINAL_POSITION";
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery(strSQL);
            while (rs.next()) {
                list.add(rs.getString(1));
            }
        } catch (Exception ex) {
            throw ex;
        } finally {
            closeCon(rs, stmt, null);
        }
        return list;
    }

    /**
     * 获取数据库表中所有字段名
     *
     * @param url      url
     * @param username 用户名
     * @param password 密码
     * @param dbname   dbname
     * @param tabname  tabname
     * @return {@link List}<{@link String}>
     */
    public static List<String> getFieldNameList(String url, String username, String password, String dbname, String tabname) {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List<String> list = new ArrayList<String>();
        try {
            conn = getConn(JDBC_DRIVER, url, username, password);// 打开连接
            String strSQL = "select COLUMN_NAME from  INFORMATION_SCHEMA.Columns where table_name='" + tabname + "' and table_schema='" + dbname + "' ORDER BY  ORDINAL_POSITION";
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery(strSQL);
            while (rs.next()) {
                list.add(rs.getString(1));
            }
        } catch (Exception ex) {
            //log.error("获取数据库url:"+url+"库:"+dbname+"表:"+tabname+"中所有字段名出现异常");
        } finally {
            closeCon(rs, stmt, conn);
        }
        return list;
    }


    /**
     * 查询表字段信息
     *
     * @param conn      连接
     * @param dbName    库名
     * @param tableName 表名
     * @return {@link List}<{@link Map}<{@link String}, {@link String}>>
     */
    public static List<Map<String, String>> getTableFieldInfoBySchema(String dbName, String tableName, Connection conn) {
        List<Map<String, String>> fieldInfoList = null;
        Statement stmt = null;
        ResultSet rs = null;
        try {
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery("SELECT COLUMN_NAME,COLUMN_COMMENT FROM information_schema.COLUMNS WHERE TABLE_SCHEMA='" + dbName + "' and TABLE_NAME='" + tableName + "' ORDER BY ORDINAL_POSITION");
            if (rs != null) {
                fieldInfoList = new ArrayList<>();
                while (rs.next()) {
                    Map<String, String> fieldInfoMap = new HashMap<>();
                    fieldInfoMap.put("fieldName", rs.getString("COLUMN_NAME"));
                    fieldInfoMap.put("fieldCName", rs.getString("COLUMN_COMMENT"));
                    fieldInfoList.add(fieldInfoMap);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, null);
        }
        return fieldInfoList;
    }


    /**
     * 查询表信息
     *
     * @param conn      连接
     * @param dbName    库名
     * @param tableName 表名
     * @return {@link Map}<{@link String}, {@link String}>
     */
    public static Map<String, String> getTableInfoBySchema(String dbName, String tableName, Connection conn) {
        Map<String, String> tableInfoMap = new HashMap<>();
        Statement stmt = null;
        ResultSet rs = null;
        try {
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery("SELECT TABLE_NAME,TABLE_COMMENT FROM information_schema.TABLES WHERE TABLE_SCHEMA='" + dbName + "' and TABLE_NAME='" + tableName + "'");
            if (rs != null && rs.next()) {
                tableInfoMap.put("tableName", rs.getString("TABLE_NAME"));
                tableInfoMap.put("tableCName", rs.getString("TABLE_COMMENT"));
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, null);
        }
        return tableInfoMap;
    }


    /**
     * 获取数据库表中前100条数据
     *
     * @param conn    连接
     * @param dbname  库名
     * @param tabname 表名
     * @return int
     * @throws SQLException sqlexception异常
     */
    public static int getTabDataCount(Connection conn, String dbname, String tabname) throws SQLException {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        int count = 0;
        try {
            String strSQL = "select count(*) from " + tabname;
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            while (rs.next()) {
                count = rs.getInt(1);
            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
            throw ex;
        } finally {
            closeCon(rs, stmt, conn);
        }
        return count;
    }

    /**
     * 获取数据库表中数据条数
     *
     * @param dburl    dburl
     * @param username 用户名
     * @param password 密码
     * @param dbname   dbname
     * @param tabname  tabname
     * @return int
     */
    public static int getTabDataCount(String dburl, String username, String password, String dbname, String tabname) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        int count = 0;
        try {
            String strSQL = "select count(*) from " + dbname + "." + tabname + " ";
            conn = getConn(JDBC_DRIVER, dburl, username, password);// 打开连接
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            while (rs.next()) {
                count = rs.getInt(1);
            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
        } finally {
            closeCon(rs, stmt, conn);
        }
        return count;
    }

    /**
     * 释放连接
     *
     * @param rs：结果集
     * @param st：预编译的SQL语句的对象
     * @param conn：连接对象
     */
    public static void closeCon(ResultSet rs, Statement st, Connection conn) {
        try {
            if (rs != null) {
                rs.close(); // 关闭结果集
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            try {
                if (st != null) {
                    st.close(); // 关闭Statement
                }
            } catch (SQLException e) {
                e.printStackTrace();
            } finally {
                try {
                    if (conn != null) {
                        conn.close(); // 关闭连接
                    }
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
    }


    @Override
    protected Map<String, Object> getInsert2TableSqlAndPatams(List<Map<String, String>> objList, String dbName, String tableName) {
        return null;
    }

    @Override
    protected String getCreateTableSql(List<Map<String, String>> fieldInfoList, Map<String, String> obj, String tableName, List<String> maskfields) {
        return null;
    }

    @Override
    public Map<String, Object> getInsert2TableSqlAndPatams(int start, int end, List<Map<String, String>> objList, String dbname, String tableName, String fieldnames) {
        return null;
    }


}
