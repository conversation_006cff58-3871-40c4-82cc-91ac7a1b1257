@echo off
echo 测试代理端口HTTPS配置...
echo.

echo 1. 检查证书文件是否存在...
if exist "src\main\resources\keystore.p12" (
    echo ✅ 证书文件存在: src\main\resources\keystore.p12
) else (
    echo ❌ 证书文件不存在，请先运行 generate-ssl-cert.bat
    pause
    exit /b 1
)

echo.
echo 2. 检查端口监听状态...
netstat -ano | findstr :9090 >nul
if %errorlevel% equ 0 (
    echo ✅ 主端口 9090 正在监听
) else (
    echo ❌ 主端口 9090 未监听，请先启动服务
)

netstat -ano | findstr :8081 >nul
if %errorlevel% equ 0 (
    echo ✅ 代理端口 8081 正在监听
) else (
    echo ❌ 代理端口 8081 未监听
)

netstat -ano | findstr :8082 >nul
if %errorlevel% equ 0 (
    echo ✅ 代理端口 8082 正在监听
) else (
    echo ❌ 代理端口 8082 未监听
)

echo.
echo 3. 测试HTTPS访问...
echo 测试主端口HTTPS访问...
powershell -Command "try { [System.Net.ServicePointManager]::ServerCertificateValidationCallback = {$true}; $response = Invoke-WebRequest -Uri 'https://localhost:9090/actuator/health' -UseBasicParsing -TimeoutSec 5; Write-Host '✅ 主端口HTTPS访问成功:' $response.StatusCode } catch { Write-Host '❌ 主端口HTTPS访问失败:' $_.Exception.Message }"

echo.
echo 测试代理端口HTTPS访问...
powershell -Command "try { [System.Net.ServicePointManager]::ServerCertificateValidationCallback = {$true}; $response = Invoke-WebRequest -Uri 'https://localhost:8081/' -UseBasicParsing -TimeoutSec 5; Write-Host '✅ 代理端口8081 HTTPS访问成功:' $response.StatusCode } catch { Write-Host '❌ 代理端口8081 HTTPS访问失败:' $_.Exception.Message }"

echo.
echo 4. 配置说明...
echo 要启用代理端口HTTPS，请在application.yml中设置：
echo   dwts:
echo     web-proxy:
echo       proxy-ports-ssl:
echo         enabled: true
echo.
echo 要禁用代理端口HTTPS，请设置：
echo   dwts:
echo     web-proxy:
echo       proxy-ports-ssl:
echo         enabled: false
echo.

pause
