@echo off
echo 验证SSL证书路径修复...
echo.

echo 1. 检查证书文件是否存在...
if exist "src\main\resources\keystore.p12" (
    echo ✅ 证书文件存在: src\main\resources\keystore.p12
    
    echo.
    echo 2. 检查证书文件详细信息...
    keytool -list -keystore src\main\resources\keystore.p12 -storepass changeit -storetype PKCS12 2>nul
    if %errorlevel% equ 0 (
        echo ✅ 证书文件有效
    ) else (
        echo ❌ 证书文件可能损坏
    )
) else (
    echo ❌ 证书文件不存在，正在生成...
    call generate-ssl-cert.bat
)

echo.
echo 3. 检查配置文件...
findstr "classpath:keystore.p12" src\main\resources\application.yml >nul
if %errorlevel% equ 0 (
    echo ✅ application.yml 已配置 classpath 路径
) else (
    echo ❌ application.yml 未配置 classpath 路径
)

echo.
echo 4. 当前配置状态...
echo 证书路径: classpath:keystore.p12
echo 证书密码: changeit
echo 证书类型: PKCS12
echo 证书别名: tomcat

echo.
echo 5. 重启建议...
echo 修复完成后，请重启服务以应用更改：
echo   mvn spring-boot:run
echo.
echo 重启后应该看到类似日志：
echo   "为端口8081配置HTTPS支持"
echo   "端口8081已配置HTTPS支持"
echo.
echo 而不是：
echo   "SSL证书文件不存在: src/main/resources/keystore.p12，端口8081将使用HTTP"

pause
