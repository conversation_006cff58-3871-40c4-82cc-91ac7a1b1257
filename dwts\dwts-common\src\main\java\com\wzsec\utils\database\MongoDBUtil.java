package com.wzsec.utils.database;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.mongodb.MongoClientSettings;
import com.mongodb.MongoCredential;
import com.mongodb.MongoNamespace;
import com.mongodb.ServerAddress;
import com.mongodb.client.*;
import com.mongodb.client.model.Aggregates;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class MongoDBUtil {

    private static final String DEFAULT_DATABASE = "admin";//默认数据库


    /**
     * 获取连接
     *
     * @param url      url
     * @param username 使用者
     * @param password 密码
     * @return {@link MongoClient}
     */
    public static MongoClient getConnect(String url, String username, String password, String dbname) throws Exception {
        // TODO 认证数据库设置
        MongoClient mongoClient = null;
        List<ServerAddress> hosts = new ArrayList<>();
        if (url.contains(",")) {
            String[] split = url.split(",");
            for (String serverAddress : split) {
                String[] address = serverAddress.split(":");
                hosts.add(new ServerAddress(address[0], Integer.parseInt(address[1])));
            }
        } else {
            String[] address = url.split(":");
            hosts.add(new ServerAddress(address[0], Integer.parseInt(address[1])));
        }

        try {
            //admin用户，database需要固定传参admin；其他账户则传参所属库名
            String database = DEFAULT_DATABASE.equalsIgnoreCase(username) ? DEFAULT_DATABASE : dbname;
            MongoCredential credential = MongoCredential.createCredential(username, database, password.toCharArray());
            mongoClient = MongoClients.create(
                    MongoClientSettings.builder()
                            .applyToClusterSettings(builder ->
                                    builder.hosts(hosts))
                            .credential(credential)
                            .build());
            // TODO 获取MongoClient后数据库不一定可用，试一下是否能正常获取库名，能获取说明MongoDB连接正常可用
            String[] databaseNames = mongoClient.listDatabaseNames().into(new ArrayList<>()).toArray(new String[0]);
        } catch (Exception e) {
            e.printStackTrace();
            mongoClient = null;
        }
        // 返回连接数据库对象
        return mongoClient;
    }


    /**
     * 获取库下集合(Map<库名,(集合1,集合2)>)
     *
     * @param mongoClient mongo客户端
     * @param dbName      数据库名称
     * @return {@link Map}<{@link String}, {@link String}>
     */
    public static Map<String, String> getAllDbAndTabMap(MongoClient mongoClient, String dbName) throws Exception {
        Map<String, String> dbTabMap = new HashMap<String, String>();
        // 获取指定数据库
        MongoDatabase database = mongoClient.getDatabase(dbName);

        // 获取所有集合的迭代器
        MongoIterable<String> collectionNames = database.listCollectionNames();

        List<String> collectionNameList = new ArrayList<>();
        // 遍历打印所有集合名
        for (String collectionName : collectionNames) {
            collectionNameList.add(collectionName);
        }
        dbTabMap.put(dbName, collectionNameList.stream().collect(Collectors.joining(",")));
        return dbTabMap;
    }

    /**
     * 获取文档列表(Document)
     *
     * @param mongoClient    mongo客户端
     * @param dbname         数据库
     * @param collectionName 集合
     * @return {@link List}<{@link String}>
     */
    public static List<String> getFieldNameList(MongoClient mongoClient, String dbname, String collectionName) throws Exception {

        Set<String> fieldSet = new LinkedHashSet<>();
        // 获取指定数据库
        MongoDatabase database = mongoClient.getDatabase(dbname);
        // 获取指定集合
        MongoCollection<Document> collection = database.getCollection(collectionName);

        // 构建聚合管道
        AggregateIterable<Document> randomDocuments = collection.aggregate(
                Arrays.asList(Aggregates.sample(10)));// 获取随机的lineNum条文档

        // 获取集合中所有文档的字段名
        for (Document document : randomDocuments) {
            for (String fieldName : document.keySet()) {
                if (!fieldName.equalsIgnoreCase("_id")) {
                    fieldSet.add(fieldName);
                }
            }
        }
        return new ArrayList<>(fieldSet);
    }


    /**
     * 获取集合数据
     *
     * @param mongoClient    mongo客户端
     * @param dbname         数据库
     * @param collectionName 集合名称
     * @param lineNum        数据行数
     * @return {@link List}<{@link String[]}>
     */
    public static List<String[]> getTabDataList(MongoClient mongoClient, String dbname, String collectionName, Integer lineNum) throws Exception {

        List<String[]> tabDataList = new ArrayList<>();
        try {
            // 获取指定数据库
            MongoDatabase database = mongoClient.getDatabase(dbname);
            // 获取指定集合
            MongoCollection<Document> collection = database.getCollection(collectionName);
            // TODO  构建聚合管道 ,改为随机取值1条
            // AggregateIterable<Document> randomDocuments = collection.aggregate(
            //         Arrays.asList(Aggregates.sample(lineNum)));// 获取随机的lineNum条文档
            AggregateIterable<Document> randomDocuments = collection.aggregate(Arrays.asList(
                    new Document("$sample", new Document("size", 1))));  // 获取随机的1条文档

            List<String> fieldNameList = getFieldNameList(mongoClient, dbname, collectionName);

            // 输出结果
            for (Document document : randomDocuments) {
                JSONObject jsonObject = JSONUtil.parseObj(document.toJson());
                ArrayList<String> list = new ArrayList<>();
                for (String field : fieldNameList) {
                    String str = "";
                    try {
                        str = jsonObject.getStr(field);
                        if (str.contains("$numberLong")) {
                            str = JSONUtil.parseObj(str).getStr("$numberLong");
                        }
                    } catch (Exception e) {
                        str = "";
                    }
                    list.add(str);
                }
                String[] strings = list.toArray(new String[0]);
                tabDataList.add(strings);
            }

        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            throw ex;
        }
        return tabDataList;
    }


    /**
     * 获取文档字段信息(无字段中文名,置为空值)
     *
     * @param dbname         数据库名称
     * @param collectionName 集合名称
     * @param mongoClient    mongo客户端
     * @return {@link List}<{@link Map}<{@link String}, {@link String}>>
     */
    public static List<Map<String, String>> getTableFieldInfoBySchema(String dbname, String collectionName, MongoClient mongoClient) throws Exception {
        List<Map<String, String>> fieldInfoList = new ArrayList<>();
        List<String> fieldNameList = getFieldNameList(mongoClient, dbname, collectionName);

        for (String fieldName : fieldNameList) {
            Map<String, String> fieldInfoMap = new HashMap<>();
            fieldInfoMap.put("fieldName", fieldName);
            fieldInfoMap.put("fieldCName", "");
            fieldInfoList.add(fieldInfoMap);
        }
        return fieldInfoList;
    }


    /**
     * 获取集合信息(无集合中文备注,置为空值)
     *
     * @param dbname         数据库名称
     * @param collectionName 集合名称
     * @param mongoClient    mongo客户端
     * @return {@link Map}<{@link String}, {@link String}>
     */
    public static Map<String, String> getTableInfoBySchema(String dbname, String collectionName, MongoClient mongoClient) throws Exception {
        Map<String, String> tableInfoMap = new HashMap<>();
        tableInfoMap.put("tableName", collectionName);
        tableInfoMap.put("tableCName", "");
        return tableInfoMap;
    }


    /**
     * 文档条数
     *
     * @param dbname         数据库名称
     * @param collectionName 集合名称
     * @param mongoClient    mongo客户端
     * @return {@link Integer}
     */
    public static long countDocuments(String dbname, String collectionName, MongoClient mongoClient) throws Exception {
        // 获取数据库
        MongoDatabase database = mongoClient.getDatabase(dbname);
        // 获取集合
        MongoCollection<Document> collection = database.getCollection(collectionName);
        // 使用 countDocuments 方法获取文档总数
        long documentCount = collection.countDocuments();
        return documentCount;
    }


    /**
     * 批读取文档
     *
     * @param dbname         数据库名称
     * @param collectionName 集合名称
     * @param mongoClient    mongo客户端
     * @param skip       开始下标
     * @param limit     数据条数
     * @return {@link List}<{@link Document}>
     */
    public static List<Document> batchReadDocument(String dbname, String collectionName, MongoClient mongoClient,
                                                   int skip, int limit) throws Exception {

        List<Document> documentList = new ArrayList<>();
        // 获取数据库
        MongoDatabase database = mongoClient.getDatabase(dbname);
        // 获取集合
        MongoCollection<Document> collection = database.getCollection(collectionName);

        // 执行查询并限制返回的文档数量
        FindIterable<Document> documents = collection.find().skip(skip).limit(limit);

        // 遍历结果
        for (Document document : documents) {
            documentList.add(document);
        }
        return documentList;
    }


    /**
     * 创建集合
     *
     * @param dbname              数据库名称
     * @param writeCollectionName 要写入集合的名称
     * @param mongoClient         mongo客户端
     */
    public static Boolean createCollection(String dbname, String writeCollectionName, MongoClient mongoClient) throws Exception {
        // 获取数据库
        MongoDatabase database = mongoClient.getDatabase(dbname);
        // 检查集合是否存在
        boolean collectionExists = database.listCollectionNames().into(new ArrayList<>()).contains(writeCollectionName);

        if (collectionExists) {
            log.error("数据库: {}, 集合: {}已存在,请移除后再自行任务", dbname, writeCollectionName);
            throw new RuntimeException("集合" + writeCollectionName + "已存在");    //TODO 集合已存在情况
        } else {
            // 创建集合
            database.createCollection(writeCollectionName);
        }
        return true;
    }


    /**
     * 数据写入集合
     *
     * @param dbname              数据库名称
     * @param writeCollectionName 要写入集合的名称
     * @param mongoClient         mongo客户端
     * @param documentList        要写入的文档
     */
    public static void dataWriteCollection(String dbname, String writeCollectionName,
                                           MongoClient mongoClient, List<Document> documentList) throws Exception {
        // 获取数据库
        MongoDatabase database = mongoClient.getDatabase(dbname);
        // 获取集合
        MongoCollection<Document> collection = database.getCollection(writeCollectionName);
        // 插入多个文档
        if (documentList.size() > 0) {
            collection.insertMany(documentList);
        }
    }


    /**
     * 集合重命名
     *
     * @param outdbname         输出库库名
     * @param oldCollectionName 旧集合名称
     * @param newCollectionName 新集合名称
     * @param mongoClient       mongo客户端
     */
    public static void collectionRenaming(String outdbname, String oldCollectionName, String newCollectionName,
                                          MongoClient mongoClient) throws Exception {
        // 获取数据库
        MongoDatabase database = mongoClient.getDatabase(outdbname);
        // 构造新集合的 MongoNamespace 对象
        MongoNamespace newNamespace = new MongoNamespace(database.getName(), newCollectionName);
        // 执行集合重命名操作
        database.getCollection(oldCollectionName).renameCollection(newNamespace);
    }

    /**
     * 删除集合
     *
     * @param outputDatabaseName   输出数据库名称
     * @param removeCollectionName 删除集合名称
     * @param mongoClient          mongo客户端
     * @throws Exception 例外
     */
    public static void deleteCollection(String outputDatabaseName, String removeCollectionName, MongoClient mongoClient) throws Exception {
        MongoDatabase database = mongoClient.getDatabase(outputDatabaseName);
        database.getCollection(removeCollectionName).drop();
    }


}
