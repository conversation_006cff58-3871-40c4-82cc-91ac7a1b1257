package com.wzsec.webproxy.util;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.ServerSocket;
import java.net.Socket;

/**
 * 端口检查工具
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Slf4j
public class PortChecker {

    /**
     * 检查端口是否可用（未被占用）
     */
    public static boolean isPortAvailable(int port) {
        try (ServerSocket serverSocket = new ServerSocket()) {
            serverSocket.setReuseAddress(false);
            serverSocket.bind(new InetSocketAddress("localhost", port));
            return true;
        } catch (IOException e) {
            return false;
        }
    }

    /**
     * 检查端口是否正在监听
     */
    public static boolean isPortListening(String host, int port) {
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(host, port), 1000);
            return true;
        } catch (IOException e) {
            return false;
        }
    }

    /**
     * 查找可用端口
     */
    public static int findAvailablePort(int startPort, int endPort) {
        for (int port = startPort; port <= endPort; port++) {
            if (isPortAvailable(port)) {
                return port;
            }
        }
        return -1;
    }

    /**
     * 检查多个端口的状态
     */
    public static void checkPorts(int... ports) {
        log.info("检查端口状态:");
        for (int port : ports) {
            boolean available = isPortAvailable(port);
            boolean listening = isPortListening("localhost", port);
            
            String status;
            if (available) {
                status = "可用";
            } else if (listening) {
                status = "被占用且正在监听";
            } else {
                status = "被占用但未响应";
            }
            
            log.info("端口 {}: {}", port, status);
        }
    }

    /**
     * 获取端口占用进程信息（Windows）
     */
    public static String getPortProcess(int port) {
        try {
            Process process = Runtime.getRuntime().exec("netstat -ano | findstr :" + port);
            java.io.BufferedReader reader = new java.io.BufferedReader(
                new java.io.InputStreamReader(process.getInputStream(), "GBK"));
            
            StringBuilder result = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                result.append(line).append("\n");
            }
            
            return result.toString();
            
        } catch (Exception e) {
            log.warn("获取端口进程信息失败: {}", e.getMessage());
            return "无法获取进程信息";
        }
    }

    /**
     * 主方法，用于测试
     */
    public static void main(String[] args) {
        // 检查常用端口
        checkPorts(8080, 8081, 8082, 9090);
        
        // 检查8080端口的详细信息
        if (!isPortAvailable(8080)) {
            log.info("8080端口占用详情:");
            log.info(getPortProcess(8080));
        }
        
        // 查找可用端口
        int availablePort = findAvailablePort(8083, 8090);
        if (availablePort != -1) {
            log.info("找到可用端口: {}", availablePort);
        } else {
            log.warn("在8083-8090范围内未找到可用端口");
        }
    }
}
