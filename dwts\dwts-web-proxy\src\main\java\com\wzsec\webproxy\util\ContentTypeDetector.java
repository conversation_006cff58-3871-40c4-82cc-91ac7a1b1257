package com.wzsec.webproxy.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * 内容类型检测工具
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Slf4j
@Component
public class ContentTypeDetector {

    /**
     * 检测内容类型
     *
     * @param content     内容字节数组
     * @param contentType HTTP头中的Content-Type
     * @param requestPath 请求路径
     * @return 检测到的内容类型
     */
    public String detectContentType(byte[] content, String contentType, String requestPath) {
        // 1. 优先使用HTTP头中的Content-Type
        if (contentType != null && !contentType.trim().isEmpty()) {
            return contentType.toLowerCase();
        }
        
        // 2. 根据请求路径后缀判断
        String pathBasedType = detectByPath(requestPath);
        if (pathBasedType != null) {
            return pathBasedType;
        }
        
        // 3. 根据内容特征判断
        String contentBasedType = detectByContent(content);
        if (contentBasedType != null) {
            return contentBasedType;
        }
        
        // 4. 默认返回未知类型
        return "application/octet-stream";
    }

    /**
     * 根据请求路径检测内容类型
     */
    private String detectByPath(String requestPath) {
        if (requestPath == null) {
            return null;
        }
        
        String lowerPath = requestPath.toLowerCase();
        
        // HTML文件
        if (lowerPath.endsWith(".html") || lowerPath.endsWith(".htm")) {
            return "text/html";
        }
        
        // CSS文件
        if (lowerPath.endsWith(".css")) {
            return "text/css";
        }
        
        // JavaScript文件
        if (lowerPath.endsWith(".js")) {
            return "application/javascript";
        }
        
        // JSON文件
        if (lowerPath.endsWith(".json")) {
            return "application/json";
        }
        
        // XML文件
        if (lowerPath.endsWith(".xml")) {
            return "application/xml";
        }
        
        // 图片文件
        if (lowerPath.endsWith(".png")) {
            return "image/png";
        }
        if (lowerPath.endsWith(".jpg") || lowerPath.endsWith(".jpeg")) {
            return "image/jpeg";
        }
        if (lowerPath.endsWith(".gif")) {
            return "image/gif";
        }
        if (lowerPath.endsWith(".svg")) {
            return "image/svg+xml";
        }
        if (lowerPath.endsWith(".ico")) {
            return "image/x-icon";
        }
        
        // 字体文件
        if (lowerPath.endsWith(".woff")) {
            return "font/woff";
        }
        if (lowerPath.endsWith(".woff2")) {
            return "font/woff2";
        }
        if (lowerPath.endsWith(".ttf")) {
            return "font/ttf";
        }
        
        // API路径判断
        if (lowerPath.contains("/api/") || lowerPath.contains("/rest/") || lowerPath.contains("/service/")) {
            return "application/json"; // API默认返回JSON
        }
        
        return null;
    }

    /**
     * 根据内容特征检测内容类型
     */
    private String detectByContent(byte[] content) {
        if (content == null || content.length == 0) {
            return null;
        }
        
        try {
            // 只检查前1024字节
            int checkLength = Math.min(content.length, 1024);
            String contentStr = new String(content, 0, checkLength, StandardCharsets.UTF_8).trim();
            
            if (contentStr.isEmpty()) {
                return null;
            }
            
            String lowerContent = contentStr.toLowerCase();
            
            // HTML检测
            if (lowerContent.startsWith("<!doctype html") || 
                lowerContent.startsWith("<html") ||
                lowerContent.contains("<html>") ||
                lowerContent.contains("<head>") ||
                lowerContent.contains("<body>")) {
                return "text/html";
            }
            
            // XML检测
            if (lowerContent.startsWith("<?xml") || 
                (lowerContent.startsWith("<") && lowerContent.contains("xmlns"))) {
                return "application/xml";
            }
            
            // JSON检测
            if ((lowerContent.startsWith("{") && lowerContent.endsWith("}")) ||
                (lowerContent.startsWith("[") && lowerContent.endsWith("]"))) {
                // 进一步验证是否为有效JSON
                if (isValidJsonLike(lowerContent)) {
                    return "application/json";
                }
            }
            
            // CSS检测
            if (lowerContent.contains("{") && lowerContent.contains("}") && 
                (lowerContent.contains("color:") || lowerContent.contains("font-") || 
                 lowerContent.contains("margin:") || lowerContent.contains("padding:"))) {
                return "text/css";
            }
            
            // JavaScript检测
            if (lowerContent.contains("function") || lowerContent.contains("var ") ||
                lowerContent.contains("let ") || lowerContent.contains("const ") ||
                lowerContent.contains("document.") || lowerContent.contains("window.")) {
                return "application/javascript";
            }
            
        } catch (Exception e) {
            log.debug("内容类型检测失败", e);
        }
        
        return null;
    }

    /**
     * 简单验证是否像JSON格式
     */
    private boolean isValidJsonLike(String content) {
        // 简单的JSON格式验证
        int braceCount = 0;
        int bracketCount = 0;
        boolean inString = false;
        boolean escaped = false;
        
        for (char c : content.toCharArray()) {
            if (escaped) {
                escaped = false;
                continue;
            }
            
            if (c == '\\') {
                escaped = true;
                continue;
            }
            
            if (c == '"' && !escaped) {
                inString = !inString;
                continue;
            }
            
            if (inString) {
                continue;
            }
            
            switch (c) {
                case '{':
                    braceCount++;
                    break;
                case '}':
                    braceCount--;
                    break;
                case '[':
                    bracketCount++;
                    break;
                case ']':
                    bracketCount--;
                    break;
            }
        }
        
        return braceCount == 0 && bracketCount == 0;
    }

    /**
     * 检查是否为HTML内容
     */
    public boolean isHtmlContent(String contentType) {
        return contentType != null && contentType.toLowerCase().contains("text/html");
    }

    /**
     * 检查是否为JSON内容
     */
    public boolean isJsonContent(String contentType) {
        return contentType != null && 
               (contentType.toLowerCase().contains("application/json") ||
                contentType.toLowerCase().contains("text/json"));
    }

    /**
     * 检查是否为XML内容
     */
    public boolean isXmlContent(String contentType) {
        return contentType != null && 
               (contentType.toLowerCase().contains("application/xml") ||
                contentType.toLowerCase().contains("text/xml") ||
                contentType.toLowerCase().contains("application/soap+xml"));
    }

    /**
     * 检查是否为静态资源
     */
    public boolean isStaticResource(String contentType, String requestPath) {
        if (contentType != null) {
            String lowerType = contentType.toLowerCase();
            if (lowerType.contains("image/") || 
                lowerType.contains("font/") ||
                lowerType.contains("text/css") ||
                lowerType.contains("application/javascript")) {
                return true;
            }
        }
        
        if (requestPath != null) {
            String lowerPath = requestPath.toLowerCase();
            return lowerPath.matches(".*\\.(css|js|png|jpg|jpeg|gif|ico|woff|woff2|ttf|svg)$");
        }
        
        return false;
    }
}
