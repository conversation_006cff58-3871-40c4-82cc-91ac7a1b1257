package com.wzsec.utils.file;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.encryption.InvalidPasswordException;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.pdfbox.text.PDFTextStripperByArea;

import java.io.File;
import java.io.IOException;

/**
 * PDF文件
 *
 * <AUTHOR>
 * @date 2020-4-26
 */
public class PDFUtil {

    /**
     * 读取pdf文本内容
     *
     * @param filePath 文件地址
     * @return String
     * @description 处理pdf
     * <AUTHOR>
     * @date 2020-4-26
     */
    public static String getPDFStr(String filePath) {
        String result = null;
        try (PDDocument document = PDDocument.load(new File(filePath))) {
            document.getClass();
            if (!document.isEncrypted()) {
                PDFTextStripperByArea stripper = new PDFTextStripperByArea();
                stripper.setSortByPosition(true);
                PDFTextStripper tStripper = new PDFTextStripper();
                result = tStripper.getText(document);
            }
        } catch (InvalidPasswordException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }
}
