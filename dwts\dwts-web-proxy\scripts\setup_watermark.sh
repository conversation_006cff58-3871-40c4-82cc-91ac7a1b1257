#!/bin/bash

# API 暗水印系统快速配置脚本
# 作者: JOY
# 日期: 2025/08/05

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
DB_HOST=${DB_HOST:-localhost}
DB_PORT=${DB_PORT:-3306}
DB_NAME=${DB_NAME:-dwts}
DB_USER=${DB_USER:-root}
APP_PORT=${APP_PORT:-9090}
BASE_URL="http://localhost:${APP_PORT}"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查 curl
    if ! command -v curl &> /dev/null; then
        log_error "curl 未安装，请先安装 curl"
        exit 1
    fi
    
    # 检查 jq
    if ! command -v jq &> /dev/null; then
        log_warning "jq 未安装，部分功能可能受限"
    fi
    
    # 检查 mysql 客户端
    if ! command -v mysql &> /dev/null; then
        log_warning "mysql 客户端未安装，无法自动执行数据库迁移"
    fi
    
    log_success "依赖检查完成"
}

# 数据库迁移
migrate_database() {
    log_info "执行数据库迁移..."
    
    if command -v mysql &> /dev/null; then
        read -p "请输入数据库密码: " -s DB_PASSWORD
        echo
        
        MIGRATION_FILE="$(dirname "$0")/../src/main/resources/db/migration/V1.1__Add_Invisible_Watermark_Fields.sql"
        
        if [ -f "$MIGRATION_FILE" ]; then
            mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < "$MIGRATION_FILE"
            log_success "数据库迁移完成"
        else
            log_error "迁移文件不存在: $MIGRATION_FILE"
            exit 1
        fi
    else
        log_warning "请手动执行数据库迁移脚本"
        echo "文件位置: src/main/resources/db/migration/V1.1__Add_Invisible_Watermark_Fields.sql"
    fi
}

# 等待应用启动
wait_for_app() {
    log_info "等待应用启动..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$BASE_URL/actuator/health" > /dev/null 2>&1; then
            log_success "应用启动成功"
            return 0
        fi
        
        echo -n "."
        sleep 2
        ((attempt++))
    done
    
    log_error "应用启动超时"
    exit 1
}

# 获取配置列表
get_configs() {
    log_info "获取当前配置..."
    
    response=$(curl -s "$BASE_URL/api/watermark/config/list")
    
    if [ $? -eq 0 ]; then
        if command -v jq &> /dev/null; then
            echo "$response" | jq '.data[] | {id: .id, proxyName: .proxyName, invisibleEnabled: .enableInvisibleWatermark}'
        else
            echo "$response"
        fi
    else
        log_error "获取配置失败"
        exit 1
    fi
}

# 启用暗水印
enable_invisible_watermark() {
    local config_id=$1
    local strength=${2:-medium}
    local density=${3:-0.3}
    
    log_info "为配置 ID $config_id 启用暗水印..."
    
    response=$(curl -s -X PUT "$BASE_URL/api/watermark/config/invisible/$config_id" \
        -H "Content-Type: application/json" \
        -d "{
            \"enableInvisible\": true,
            \"encodingStrength\": \"$strength\",
            \"embedDensity\": $density
        }")
    
    if [ $? -eq 0 ]; then
        if command -v jq &> /dev/null; then
            success=$(echo "$response" | jq -r '.success')
            if [ "$success" = "true" ]; then
                log_success "配置 ID $config_id 暗水印启用成功"
            else
                message=$(echo "$response" | jq -r '.message')
                log_error "启用失败: $message"
            fi
        else
            log_success "配置 ID $config_id 暗水印启用请求已发送"
        fi
    else
        log_error "启用暗水印失败"
    fi
}

# 批量启用暗水印
batch_enable_invisible() {
    log_info "批量启用暗水印..."
    
    # 获取所有配置ID
    response=$(curl -s "$BASE_URL/api/watermark/config/list")
    
    if command -v jq &> /dev/null; then
        config_ids=$(echo "$response" | jq -r '.data[].id' | tr '\n' ',' | sed 's/,$//')
        
        if [ -n "$config_ids" ]; then
            batch_response=$(curl -s -X PUT "$BASE_URL/api/watermark/config/invisible/batch" \
                -H "Content-Type: application/json" \
                -d "{
                    \"configIds\": [$config_ids],
                    \"enabled\": true
                }")
            
            success=$(echo "$batch_response" | jq -r '.success')
            if [ "$success" = "true" ]; then
                updated_count=$(echo "$batch_response" | jq -r '.updatedCount')
                total_count=$(echo "$batch_response" | jq -r '.totalCount')
                log_success "批量启用完成: $updated_count/$total_count"
            else
                message=$(echo "$batch_response" | jq -r '.message')
                log_error "批量启用失败: $message"
            fi
        else
            log_warning "未找到可配置的代理"
        fi
    else
        log_warning "需要 jq 工具进行批量操作"
    fi
}

# 验证配置
verify_setup() {
    log_info "验证配置..."
    
    # 检查统计信息
    response=$(curl -s "$BASE_URL/api/watermark/config/statistics")
    
    if [ $? -eq 0 ] && command -v jq &> /dev/null; then
        total=$(echo "$response" | jq -r '.data.totalConfigs')
        invisible_enabled=$(echo "$response" | jq -r '.data.invisibleWatermarkEnabled')
        
        log_info "配置统计:"
        echo "  总配置数: $total"
        echo "  暗水印启用: $invisible_enabled"
        
        if [ "$invisible_enabled" -gt 0 ]; then
            log_success "暗水印配置验证通过"
        else
            log_warning "暗水印未启用"
        fi
    else
        log_warning "无法获取统计信息"
    fi
    
    # 测试溯源功能
    test_response=$(curl -s -X POST "$BASE_URL/api/watermark/trace/quick-detect" \
        -H "Content-Type: application/json" \
        -d '{
            "content": "test content",
            "contentType": "text/plain"
        }')
    
    if [ $? -eq 0 ]; then
        log_success "溯源API测试通过"
    else
        log_warning "溯源API测试失败"
    fi
}

# 显示使用说明
show_usage() {
    echo "API 暗水印系统配置脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help              显示帮助信息"
    echo "  -m, --migrate           执行数据库迁移"
    echo "  -w, --wait              等待应用启动"
    echo "  -l, --list              列出当前配置"
    echo "  -e, --enable ID         启用指定配置的暗水印"
    echo "  -b, --batch             批量启用所有配置的暗水印"
    echo "  -v, --verify            验证配置"
    echo "  -a, --all               执行完整配置流程"
    echo
    echo "环境变量:"
    echo "  DB_HOST                 数据库主机 (默认: localhost)"
    echo "  DB_PORT                 数据库端口 (默认: 3306)"
    echo "  DB_NAME                 数据库名称 (默认: dwts)"
    echo "  DB_USER                 数据库用户 (默认: root)"
    echo "  APP_PORT                应用端口 (默认: 9090)"
    echo
    echo "示例:"
    echo "  $0 --all                # 执行完整配置"
    echo "  $0 --enable 1           # 启用配置ID为1的暗水印"
    echo "  $0 --batch              # 批量启用所有暗水印"
}

# 主函数
main() {
    case "$1" in
        -h|--help)
            show_usage
            ;;
        -m|--migrate)
            check_dependencies
            migrate_database
            ;;
        -w|--wait)
            wait_for_app
            ;;
        -l|--list)
            check_dependencies
            get_configs
            ;;
        -e|--enable)
            if [ -z "$2" ]; then
                log_error "请指定配置ID"
                exit 1
            fi
            check_dependencies
            enable_invisible_watermark "$2" "${3:-medium}" "${4:-0.3}"
            ;;
        -b|--batch)
            check_dependencies
            batch_enable_invisible
            ;;
        -v|--verify)
            check_dependencies
            verify_setup
            ;;
        -a|--all)
            log_info "开始完整配置流程..."
            check_dependencies
            migrate_database
            wait_for_app
            batch_enable_invisible
            verify_setup
            log_success "配置完成！"
            ;;
        "")
            show_usage
            ;;
        *)
            log_error "未知选项: $1"
            show_usage
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
