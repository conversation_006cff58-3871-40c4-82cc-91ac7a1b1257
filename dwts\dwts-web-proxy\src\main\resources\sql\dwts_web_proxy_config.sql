/*
 Navicat Premium Data Transfer

 Source Server         : 【MySQL_*************_3306_root】
 Source Server Type    : MySQL
 Source Server Version : 80028 (8.0.28)
 Source Host           : *************:3306
 Source Schema         : dwts-clean

 Target Server Type    : MySQL
 Target Server Version : 80028 (8.0.28)
 File Encoding         : 65001

 Date: 05/08/2025 15:37:37
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for dwts_web_proxy_config
-- ----------------------------
DROP TABLE IF EXISTS `dwts_web_proxy_config`;
CREATE TABLE `dwts_web_proxy_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `proxy_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '代理名称',
  `proxy_port` int NOT NULL COMMENT '代理端口',
  `target_host` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '目标主机地址',
  `target_port` int NOT NULL COMMENT '目标端口',
  `target_protocol` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'http' COMMENT '目标协议 (http/https)',
  `watermark_text` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '水印文本',
  `enable_page_watermark` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用页面水印',
  `enable_api_watermark` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用API水印',
  `api_path_patterns` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '/api/**,/rest/**' COMMENT 'API路径模式 (逗号分隔)',
  `watermark_opacity` double NULL DEFAULT 0.1 COMMENT '水印透明度 (0.0-1.0)',
  `watermark_width` int NULL DEFAULT 300 COMMENT '水印宽度',
  `watermark_height` int NULL DEFAULT 150 COMMENT '水印高度',
  `watermark_color` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '#666666' COMMENT '水印颜色',
  `watermark_angle` double NULL DEFAULT -30 COMMENT '水印角度',
  `enable_link_rewrite` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用链接重写',
  `enable_api_intercept` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用API拦截',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'ACTIVE' COMMENT '状态 (ACTIVE/INACTIVE)',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建用户',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新用户',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_proxy_port`(`proxy_port` ASC) USING BTREE,
  UNIQUE INDEX `uk_proxy_name`(`proxy_name` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_target_host_port`(`target_host` ASC, `target_port` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'Web代理配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of dwts_web_proxy_config
-- ----------------------------
INSERT INTO `dwts_web_proxy_config` VALUES (1, 'Vue应用代理示例', 8080, 'www.baidu.com', 443, 'https', 'DWTS测试水印_{IP}_{DATE}', 1, 0, '/api/**,/rest/**,/service/**', 0.3, 300, 150, '#FF0000', -30, 1, 0, 'ACTIVE', 'Vue应用代理配置示例，可以代理Vue+Nginx应用', 'system', '2025-08-05 10:27:23', NULL, '2025-08-05 14:44:07');
INSERT INTO `dwts_web_proxy_config` VALUES (2, 'API服务代理', 8081, '127.0.0.1', 8013, 'http', 'API代理_{IP}_{TIME}', 1, 0, '/api/**,/v1/**,/v2/**', 0.15, 250, 120, '#999999', -25, 1, 0, 'ACTIVE', 'API服务专用代理，只对接口添加水印', 'system', '2025-08-05 10:27:23', NULL, '2025-08-05 13:46:15');
INSERT INTO `dwts_web_proxy_config` VALUES (3, '管理后台代理', 8082, '*************', 3000, 'http', '管理后台_{USER}_{DATE}', 1, 0, '/admin/api/**,/management/**', 0.08, 350, 180, '#333333', -35, 1, 0, 'ACTIVE', '管理后台代理配置，支持用户标识水印', 'system', '2025-08-05 10:27:23', NULL, '2025-08-05 13:40:10');
INSERT INTO `dwts_web_proxy_config` VALUES (4, '测试环境代理', 8083, 'www.yuque.com', 443, 'https', 'DWTS测试水印_{IP}_{DATE}', 1, 0, '/api/**,/test/**', 0.3, 280, 140, '#FF6B6B', -20, 1, 0, 'ACTIVE', '测试环境代理（已禁用）', 'system', '2025-08-05 10:27:23', NULL, '2025-08-05 14:53:06');
INSERT INTO `dwts_web_proxy_config` VALUES (5, '配置说明', 8084, 'httpbin.org', 443, 'https', 'DWTS测试水印_{IP}_{DATE}', 1, 0, 'Ant路径模式: /api/**, /**/service/**, /v*/**, 等', 0.3, 300, 150, '颜色格式: #RGB, #RRGGBB, red, blue, green 等', -30, 1, 0, 'ACTIVE', '配置说明示例，请勿启用。透明度0.0-1.0，角度支持正负值。', 'system', '2025-08-05 10:27:23', NULL, '2025-08-05 14:59:12');

SET FOREIGN_KEY_CHECKS = 1;
