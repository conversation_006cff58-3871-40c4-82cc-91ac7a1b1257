package com.wzsec.utils.DatabaseOperation;

import cn.hutool.core.util.StrUtil;
import com.wzsec.utils.database.DatabaseUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * MySQL工具类
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
public class MysqlUtils extends DatabaseOperationUtil {

    private final static Logger log = LoggerFactory.getLogger(DatabaseUtil.class);

    private static String JDBC_DRIVER = "com.mysql.jdbc.Driver";

    @Override
    public Connection getConnection(String url, String username, String password) throws SQLException, ClassNotFoundException {
        Class.forName(JDBC_DRIVER);
        Connection con = DriverManager.getConnection(url, username, password);
        return con;
    }

    @Override
    public List<Map<String, String>> getResultSet(Connection conn, String dbName, String tableName, String condition,
                                                  Long pageSize, long StartLine, long endLine) throws SQLException {

        List<String> tabFieldList = getTabFieldList(conn, dbName, tableName);

        ResultSet rs = null;
        Statement stmt = null;
        // 查抽取字段数据
        String sql = "";
        if (org.apache.commons.lang3.StringUtils.isNotBlank(condition)) {
            String template = "select * from (select * from {}.{} {}) as a limit {},{}";
            sql = StrUtil.format(template, dbName, tableName, condition, StartLine, endLine);
        } else {
            String template = "select * from {}.{} limit {},{}";
            sql = StrUtil.format(template, dbName, tableName, StartLine, endLine);
        }
        // 将结果集转换为 List<Map>
        List<Map<String, String>> dataList = new ArrayList<>();
        try {
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();

            while (rs.next()) {
                Map<String, String> rowMap = new LinkedHashMap<>(); // 保持插入顺序

                for (String fieldName : tabFieldList) {
                    for (int i = 1; i <= columnCount; i++) {
                        String columnName = metaData.getColumnName(i);
                        if (fieldName.equalsIgnoreCase(columnName)) {
                            Object columnValue = rs.getObject(i);
                            rowMap.put(columnName, String.valueOf(columnValue));
                            break;
                        }
                    }
                }

                dataList.add(rowMap);
            }
        } finally {
            DatabaseOperationUtil.closeCon(rs, stmt, null);
        }
        return dataList;
    }

    @Override
    public Map<String, String> getAllDbTabMap(Connection conn, String dbName) throws SQLException {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Map<String, String> dbTabMap = null;
        try {
            String strSQL = "select `table_schema`,`table_name` from `information_schema`.tables ";
            if (dbName != null && !"".equals(dbName)) {
                strSQL += " where `table_schema` in ('" + dbName + "') ";
            }
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            dbTabMap = new TreeMap<String, String>();
            while (rs.next()) {
                String dbNames = rs.getString(1);
                String table_name = rs.getString(2);
                if (!"sys".equals(dbNames) && !"information_schema".equals(dbNames) && !"performance_schema".equals(dbNames)) {
                    if (dbTabMap.containsKey(dbNames)) {
                        dbTabMap.put(dbNames, dbTabMap.get(dbNames) + "," + table_name);
                    } else {
                        dbTabMap.put(dbNames, table_name);
                    }
                }
            }
        } finally {
            DatabaseOperationUtil.closeCon(rs, stmt, null);
        }
        return dbTabMap;
    }

    @Override
    public Map<String, String> getTableInfoBySchema(Connection conn, String dbName, String tableName) throws SQLException {
        Statement stmt = null;
        ResultSet rs = null;
        Map<String, String> tableInfoMap = new HashMap<>();
        try {
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery("SELECT TABLE_NAME,TABLE_COMMENT FROM information_schema.TABLES WHERE TABLE_SCHEMA='" + dbName + "' and TABLE_NAME='" + tableName + "'");
            if (rs != null && rs.next()) {
                tableInfoMap.put("tableName", rs.getString("TABLE_NAME"));
                tableInfoMap.put("tableCName", rs.getString("TABLE_COMMENT"));
            }
        } finally {
            DatabaseOperationUtil.closeCon(rs, stmt, null);
        }
        return tableInfoMap;
    }

    @Override
    public List<String> getTabFieldList(Connection conn, String dbname, String tabname) throws SQLException {
        Statement stmt = null;
        ResultSet rs = null;
        List<String> list = new ArrayList<>();
        try {
            String strSQL = "select COLUMN_NAME from  INFORMATION_SCHEMA.Columns where table_name='" + tabname + "' and table_schema='" + dbname + "' " + " order by ORDINAL_POSITION";
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery(strSQL);
            while (rs.next()) {
                list.add(rs.getString(1));
            }
        } finally {
            DatabaseOperationUtil.closeCon(rs, stmt, null);
        }
        return list;
    }

    @Override
    public List<Map<String, String>> getTableFieldInfo(Connection conn, String dbName, String tableName) throws SQLException {
        Statement stmt = null;
        ResultSet rs = null;
        List<Map<String, String>> fieldInfoList = null;
        try {
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery("SELECT COLUMN_NAME,COLUMN_COMMENT FROM information_schema.COLUMNS WHERE TABLE_SCHEMA='" + dbName + "' and TABLE_NAME='" + tableName + "' ORDER BY  ORDINAL_POSITION");
            if (rs != null) {
                fieldInfoList = new ArrayList<>();
                while (rs.next()) {
                    Map<String, String> fieldInfoMap = new HashMap<>();
                    fieldInfoMap.put("fieldName", rs.getString("COLUMN_NAME"));
                    fieldInfoMap.put("fieldCName", rs.getString("COLUMN_COMMENT"));
                    fieldInfoList.add(fieldInfoMap);
                }
            }
        } finally {
            DatabaseOperationUtil.closeCon(rs, stmt, null);
        }

        return fieldInfoList;
    }

    @Override
    public int getDataCount(Connection conn, String in_dbname, String in_tabname, String in_limitingcondition) throws SQLException {
        // 根据输入数据源类型获取统计表中数据条数
        int count = 0;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        String sql = "";
        if (org.apache.commons.lang3.StringUtils.isNotBlank(in_limitingcondition)) {
            String template = "select count(*) from (select * from {}.{} {}) as a";
            sql = StrUtil.format(template, in_dbname, in_tabname, in_limitingcondition);
        } else {
            String template = "select count(*) from {}.{}";
            sql = StrUtil.format(template, in_dbname, in_tabname);
        }
        log.info("连接mysql数据库成功");
        try {
            preparedStatement = conn.prepareStatement(sql);
            resultSet = preparedStatement.executeQuery();

            while (resultSet.next()) {
                count = (int) resultSet.getLong(1);
            }
        } finally {
            DatabaseOperationUtil.closeCon(resultSet, preparedStatement, null);
        }
        return count;
    }

    public static String getSrcCreateTableSql(Connection conn, String tableName) throws SQLException {
        String srcCreateTableSql = null;
        Statement stmt = null;
        ResultSet rs = null;
        try {
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery("show create table `" + tableName + "`;");
            if (rs != null) {
                while (rs.next()) {
                    srcCreateTableSql = rs.getString("Create Table");
                }
            }
        } finally {
            DatabaseOperationUtil.closeCon(rs, stmt, null);
        }
        return srcCreateTableSql;
    }

    @Override
    public String getCreateTableSql(String dbName, String tableName,
                                    List<Map<String, String>> fieldInfoList,
                                    Map<String, String> obj,
                                    List<String> maskfields, String srcSql) {
        Set<String> keySet = obj.keySet();
        StringBuffer sb = new StringBuffer();
        StringBuffer end_sb = new StringBuffer();
        String end_sql = srcSql.substring(srcSql.lastIndexOf(")"), srcSql.length());
        String middle_sql = srcSql.substring(srcSql.indexOf("(") + 1, srcSql.lastIndexOf(")"));
        String[] srcfilemiddlesql = middle_sql.split(",");
        HashMap<String, String> srcfieldsql = new HashMap<String, String>();
        for (String s : srcfilemiddlesql) {
            String str = s.trim();
            String fieldname = str.substring(1, str.lastIndexOf("`"));
            if (str.startsWith("`")) {
                srcfieldsql.put(fieldname, str);
            } else {
                for (String extractfieldnamestr : keySet) {
                    if (str.contains(extractfieldnamestr)) {
                        end_sb.append(str).append(",\r\n");
                    }
                }
            }
        }
        sb.append("CREATE TABLE `").append(tableName).append("` (\r\n");
        for (Map<String, String> fieldInfo : fieldInfoList) {
            String field = fieldInfo.get("fieldName");
            if (!keySet.contains(field)) {// 跳过没有抽取的列
                continue;
            }
            String fieldsql = srcfieldsql.get(field);
            //log.info(fieldsql);
            if (maskfields != null && maskfields.contains(field)) {// 加解密的字段类型更改为varchar
                int post = appearNumber(fieldsql, " ", 2);
                String str = post != 0 ? fieldsql.substring(post, fieldsql.length()) : "";
                sb.append("`" + field + "`");
                sb.append(" longtext ");// 类型
                int autoIndex = str.indexOf("AUTO_INCREMENT");
                if (autoIndex >= 0) {
                    str = str.replace("AUTO_INCREMENT", "");
                }
                sb.append(str).append(",\r\n");
            } else {
                sb.append(fieldsql).append(",\r\n");
            }

        }
        sb.append(end_sb.toString());
        int lastIndex = sb.lastIndexOf(",");// 去掉最后一个逗号
        int endAutoIndex = end_sql.indexOf("AUTO_INCREMENT");
        if (endAutoIndex >= 0) {
            String autoSql = end_sql.substring(endAutoIndex);
            autoSql = autoSql.substring(autoSql.indexOf("AUTO_INCREMENT"), autoSql.indexOf(" "));
            end_sql = end_sql.replace(autoSql, "");
        }
        return sb.substring(0, lastIndex) + ")";
    }

    @Override
    public Map<String, Object> getBatchInsertionInformation(int start, int end, String dbname, String tableName, List<Map<String, String>> objList) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO `").append(tableName).append("` (");
            for (String column : fields) {
                sb.append("`").append(column).append("`, ");
            }
            String sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sb = new StringBuilder(sql);
            sb.append(") VALUES ");
            for (int i = start; i < end; i++) {
                Map<String, String> map = objList.get(i);
                sb.append("(");
                for (String key : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?, ");
                    params.add(map.get(key));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append("), ");
            }
            sql = sb.toString();
            lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sqlAndParams = new HashMap<>();
            sqlAndParams.put("sql", sql);
            sqlAndParams.put("params", params.toArray());
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    @Override
    public List<String[]> getTabDataList(Connection conn, String dbname, String tableName, Integer lineNum) throws SQLException {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<String[]> tabDataList = new ArrayList<>();
        String strSQL = null;
        strSQL = "select * from " + tableName;
        if (lineNum != null && lineNum != 0)
            strSQL += " order by rand() LIMIT " + lineNum;
        try {
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            ResultSetMetaData md = rs.getMetaData(); //获得结果集结构信息,元数据
            int columnCount = md.getColumnCount();   //获得列数
            while (rs.next()) {
                String[] row = new String[columnCount];
                for (int i = 0; i < columnCount; i++) {
                    row[i] = rs.getString(i + 1) == null ? "" : rs.getString(i + 1);
                }
                tabDataList.add(row);
            }
        } finally {
            DatabaseOperationUtil.closeCon(rs, stmt, null);
        }
        return tabDataList;
    }

    private static int appearNumber(String fieldsql, String s, int i) {
        try {
            Pattern pattern = Pattern.compile(s);
            Matcher findMatcher = pattern.matcher(fieldsql);
            int number = 0;
            while (findMatcher.find()) {
                number++;
                if (number == i) {//当“i”次出现时停止
                    break;
                }
            }
            return findMatcher.start();
        } catch (Exception ex) {
            ex.printStackTrace();
            return 0;
        }
    }


}
