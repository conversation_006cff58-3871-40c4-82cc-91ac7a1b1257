-- 启用暗水印配置脚本
-- 请根据实际的代理配置ID或名称修改WHERE条件

-- 方式1：根据代理端口启用（假设您的代理端口是9090）
UPDATE dwts_web_proxy_config 
SET 
    enable_api_watermark = false,              -- 关闭可见API水印
    enable_invisible_watermark = true,         -- 启用暗水印
    invisible_encoding_strength = 'medium',    -- 中等编码强度
    invisible_embed_density = 0.3,             -- 嵌入密度30%
    api_path_patterns = '/api/**,/rest/**,/service/**,/**/api/**'  -- API路径模式
WHERE proxy_port = 9090;

-- 方式2：根据代理名称启用
-- UPDATE dwts_web_proxy_config 
-- SET 
--     enable_api_watermark = false,
--     enable_invisible_watermark = true,
--     invisible_encoding_strength = 'medium',
--     invisible_embed_density = 0.3,
--     api_path_patterns = '/api/**,/rest/**,/service/**,/**/api/**'
-- WHERE proxy_name = 'your-proxy-name';

-- 方式3：启用所有活跃配置的暗水印
-- UPDATE dwts_web_proxy_config 
-- SET 
--     enable_api_watermark = false,
--     enable_invisible_watermark = true,
--     invisible_encoding_strength = 'medium',
--     invisible_embed_density = 0.3,
--     api_path_patterns = '/api/**,/rest/**,/service/**,/**/api/**'
-- WHERE status = 'ACTIVE';

-- 查看当前配置
SELECT 
    id,
    proxy_name,
    proxy_port,
    target_host,
    target_port,
    enable_api_watermark,
    enable_invisible_watermark,
    invisible_encoding_strength,
    invisible_embed_density,
    api_path_patterns,
    status
FROM dwts_web_proxy_config 
WHERE status = 'ACTIVE';
