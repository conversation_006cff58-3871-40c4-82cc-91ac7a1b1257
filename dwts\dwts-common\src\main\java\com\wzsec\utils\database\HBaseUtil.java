/**
 * CHINA TELECOM CORPORATION CONFIDENTIAL
 * ______________________________________________________________
 * <p>
 * [2015] - [2020] China Telecom Corporation Limited,
 * All Rights Reserved.
 * <p>
 * NOTICE:  All information contained herein is, and remains
 * the property of China Telecom Corporation and its suppliers,
 * if any. The intellectual and technical concepts contained
 * herein are proprietary to China Telecom Corporation and its
 * suppliers and may be covered by China and Foreign Patents,
 * patents in process, and are protected by trade secret  or
 * copyright law. Dissemination of this information or
 * reproduction of this material is strictly forbidden unless prior
 * written permission is obtained from China Telecom Corporation.
 */
package com.wzsec.utils.database;

import com.wzsec.utils.StringUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.*;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.util.Bytes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @ClassName: HbaseUtils
 * @Description: TODO
 * @date 2019年10月25日
 */
public class HBaseUtil {

    private static Configuration configuration;

    public static Connection connection;
    public static Admin admins;

    private final static Logger log = LoggerFactory.getLogger(DatabaseUtil.class);

    public static final String ZK = "***************:2181";//zookeper配置
    //public static final String TOPIC = "ad_upload_event";
    //public static final String BROKER_LIST = "*************:9092";//kalfa配置
    //public static final String GROUP_ID = "test_group";
    public static final int CACHE_LIST_SIZE = 100; //批量提交数据条数

    ThreadLocal<List<Put>> threadLocal = new ThreadLocal<List<Put>>();
    HBaseAdmin admin = null;
    Connection conn = null;


    /**
     * 根据表名获取到HTable实例
     */
    public HTable getTable(String tableName) {

        HTable table = null;
        try {
            // table = new HTable(configuration, tableName);
            final TableName tname = TableName.valueOf(tableName);
            table = (HTable) conn.getTable(tname);

        } catch (IOException e) {
            e.printStackTrace();
        }

        return table;
    }

    /**
     * 添加单条记录到HBase表
     *
     * @param tableName HBase表名
     * @param rowkey    HBase表的rowkey
     * @param cf        HBase表的columnfamily
     * @param column    HBase表的列key
     * @param value     写入HBase表的值value
     */
    public void put(String tableName, String rowkey, String cf, String column, String value) {

        HTable table = getTable(tableName);
        Put put = new Put(Bytes.toBytes(rowkey));
        put.add(Bytes.toBytes(cf), Bytes.toBytes(column), Bytes.toBytes(value));
        try {
            table.put(put);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    /**
     * 批量添加记录到HBase表，同一线程要保证对相同表进行添加操作！
     *
     * @param tableName HBase表名
     * @param rowkey    HBase表的rowkey
     * @param cf        HBase表的columnfamily
     * @param column    HBase表的列key
     * @param value     写入HBase表的值value
     */
    public void bulkput(String tableName, String rowkey, String cf, String column, String value) {
        try {
            List<Put> list = threadLocal.get();
            if (list == null) {
                list = new ArrayList<Put>();
            }
            Put put = new Put(Bytes.toBytes(rowkey));
            put.add(Bytes.toBytes(cf), Bytes.toBytes(column), Bytes.toBytes(value));
            list.add(put);
            if (list.size() >= CACHE_LIST_SIZE) {
                HTable table = getTable(tableName);
                table.put(list);
                list.clear();
            } else {
                threadLocal.set(list);
            }
            //  table.flushCommits();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static HBaseUtil instance = null;

    public static synchronized HBaseUtil getInstance() {
        if (null == instance) {
            instance = new HBaseUtil();
        }
        return instance;
    }


    private HBaseUtil() {
        Configuration configuration = new Configuration();
        configuration.set("hbase.zookeeper.quorum", ZK);
        configuration.set("hbase.rootdir", "hdfs://************:8020/hbase");

        try {
            conn = ConnectionFactory.createConnection(configuration);
            admin = new HBaseAdmin(configuration);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    /**
     * @Description:连接hbase
     * <AUTHOR>
     * @date 2020年2月26日
     */
    public static Connection getConn(String quorum) {
        Configuration configuration = HBaseConfiguration.create();
        Connection createConnection = null;
        // String quorum =
        // ConfigurationManager.getProperty("hbase.zookeeper.quorum");
        configuration.set("hbase.zookeeper.quorum", quorum);
        try {
            createConnection = ConnectionFactory.createConnection(configuration);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return createConnection;
    }

    /**
     * @Description: 获取数据库中所有的库名表名
     * <AUTHOR>
     * @date 2020年2月26日
     */
    //  TODO >>>>>>>>>>>>>>>>>>>>>>>>>>>>> HBase >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>.hbase namespace
    public static Map<String, String> getAllDbAndTabMap(Connection conn, String dbnames) {
        Map<String, String> dbTabMap = null;
        if (dbnames != null && !"".equals(dbnames)) {
            TableName[] tns = new TableName[0];
            dbTabMap = new TreeMap<>();
            try {
                Admin admins = conn.getAdmin();
                tns = admins.listTableNamesByNamespace(dbnames);
            } catch (IOException e) {
                System.out.println("  连接失败,请重试!  ");
            }
            for (TableName t : tns) {
                System.out.println("NAMESPACE --" + dbnames + " 下的表有 : " + t.getNameAsString());
//                dbTabMap.put(dbnames, t.getNameAsString());
                if (dbTabMap.containsKey(dbnames)) {
                    dbTabMap.put(dbnames, dbTabMap.get(dbnames) + "," + t.getNameAsString());
                } else {
                    dbTabMap.put(dbnames, t.getNameAsString());
                }
            }
        }

//        TableName[] tableNames;
//        try {
//            Admin admin = conn.getAdmin();
//            tableNames = admin.listTableNames();
//            ArrayList<String> tables = new ArrayList<String>();
//            for (TableName tableName : tableNames) {
//                dbTabMap.put("default", tableName.getNameAsString());
//            }
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
        return dbTabMap;
    }


    //无库名,获取表中所有数据
//    public static List<Map<String, String>> getAllRows(Connection conn, String dbnames) throws IOException {
    public static void getAllRows(Connection conn, String dbnames) throws IOException {

        HBaseAdmin admin = new HBaseAdmin(conn);
        //如表存在即进行扫描
        if (admin.tableExists(dbnames)) {

            //HTable：封装了整个表的所有的信息（表名，列簇的信息），提供了操作该表数据所有的业务方法。
            HTable hTable = new HTable(configuration, dbnames);
            //得到用于扫描region的对象scan
            //Scan： 封装查询信息，很get有一点不同，Scan可以设置Filter
            Scan scan = new Scan();
            //使用HTable得到resultcanner实现类的对象
            ResultScanner resultScanner = hTable.getScanner(scan);
            for (Result result : resultScanner) {
                //Cell：封装了Column的所有的信息：Rowkey、qualifier、value、时间戳
                Cell[] cells = result.rawCells();
                for (Cell cell : cells) {
                    System.out.println("行键: " + Bytes.toString(CellUtil.cloneRow(cell)));
                    System.out.println("列簇: " + Bytes.toString(CellUtil.cloneFamily(cell)));
                    System.out.println("列: " + Bytes.toString(CellUtil.cloneQualifier(cell)));
                    System.out.println("值: " + Bytes.toString(CellUtil.cloneValue(cell)));
                    System.out.println();
                }
            }
        }
    }


    /**
     * 得到所有字段名 TODO >>>>>>>>>>>>>>>>>>>>>>>>>>>>> HBase >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>.
     *
     * @param dbName    数据库的名字
     * @param tableName 表名
     * @param conn
     * @return {@code List<Map<String, String>>}
     * @throws IOException ioexception
     */
    public static List<Map<String, String>> getAllFieldName(String dbName, String tableName, Connection conn, String srcurl) throws IOException {

//      getRowName(tableName);
        List<Map<String, String>> fieldInfoList = null;
        Configuration configuration = HBaseConfiguration.create();
        configuration.set("hbase.zookeeper.quorum", srcurl);
        HTable hTable = new HTable(configuration, tableName);
        Scan scan = new Scan();
        ResultScanner resultScanner = hTable.getScanner(scan);
        for (Result result : resultScanner) {
            //Cell：封装了Column的所有的信息：Rowkey、qualifier、value、时间戳
            Cell[] cells = result.rawCells();
            fieldInfoList = new ArrayList<>();
            for (Cell cell : cells) {
                System.out.println("列簇: " + Bytes.toString(CellUtil.cloneFamily(cell)));  //列簇
                System.out.println("列: " + Bytes.toString(CellUtil.cloneQualifier(cell)));  //列
                //拿到所有的列,无备注
                String cloneQualifier = Bytes.toString(CellUtil.cloneFamily(cell)) + ":" + Bytes.toString(CellUtil.cloneQualifier(cell));
                Map<String, String> fieldInfoMap = new HashMap<>();
                fieldInfoMap.put("fieldName", cloneQualifier);
                fieldInfoMap.put("fieldCName", "");
                fieldInfoList.add(fieldInfoMap);
            }
        }
        return fieldInfoList;

    }

    public static void getRowName(String tableName) throws IOException {
        Table table = connection.getTable(TableName.valueOf(tableName));
        List<String> list = new ArrayList<>();
        HTableDescriptor hTableDescriptor = table.getTableDescriptor();
        for (HColumnDescriptor fdescriptor : hTableDescriptor.getColumnFamilies()) {
            list.add(fdescriptor.getNameAsString());
        }
        for (int i = 0; i < list.size(); i++) {
            System.out.println(list.get(i));
        }
    }


    /**
     * @Description: 获取数据库表中所有字段名
     * <AUTHOR>
     * @date 2020年2月26日
     */
    public static List<String> getFieldNameList(Connection conn, String dbname,
                                                String tabname) {
        List<String> dataList = new ArrayList<String>();
        Scan scan = new Scan();
        //scan.setMaxResultSize(1);
        scan.setMaxResultsPerColumnFamily(1);
        //Connection conn = getConn(url);
        ResultScanner results;
        try {
            Table table = conn.getTable(TableName.valueOf(tabname));
            results = table.getScanner(scan);
            for (Result result : results) {
                Cell[] rawCells = result.rawCells();
                for (Cell cell : rawCells) {
                    String Qualifier = Bytes.toString(CellUtil.cloneQualifier(cell));
                    //String family = Bytes.toString(CellUtil.cloneFamily(cell));
                    dataList.add(Qualifier);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return dataList;
    }

    /**
     * @Description: 获取数据库表中数据
     * <AUTHOR>
     * @date 2020年2月26日
     */
    public static List<String[]> getTabDataList(Connection conn, String dbname, String tabname, Integer lineNum) {
        List<String[]> dataList = new ArrayList<String[]>();
        Scan scan = new Scan();
        //scan.setMaxResultSize(1);
        if (lineNum != null && lineNum != 0) {
            scan.setMaxResultsPerColumnFamily(lineNum);
        }
        //Connection conn = getConn(url);
        ResultScanner results;
        try {
            Table table = conn.getTable(TableName.valueOf(tabname));
            results = table.getScanner(scan);
            for (Result result : results) {
                Cell[] rawCells = result.rawCells();
//                for (Cell cell : rawCells) {
//                    //String Qualifier = Bytes.toString(CellUtil.cloneQualifier(cell));
//                    String Value = Bytes.toString(CellUtil.cloneValue(cell));
//                    //String family = Bytes.toString(CellUtil.cloneFamily(cell));
//                    dataList.add(Value);
//                }

                String[] row = new String[rawCells.length];
                for (int i = 0; i < rawCells.length; i++) {
                    //String Qualifier = Bytes.toString(CellUtil.cloneQualifier(cell));
                    String Value = Bytes.toString(CellUtil.cloneValue(rawCells[i]));
                    //String family = Bytes.toString(CellUtil.cloneFamily(cell));
                    row[i] = Value;
                }
                dataList.add(row);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return dataList;
    }


//    public static List<String[]> getTabDataList(Connection conn, String dbname, String tabname, Integer lineNum) {
//        List<String[]> dataList = new ArrayList<String[]>();
//        Scan scan = new Scan();
//        //scan.setMaxResultSize(1);
//        if (lineNum != null && lineNum != 0) {
//            scan.setMaxResultsPerColumnFamily(lineNum);
//        }
//        //Connection conn = getConn(url);
//        ResultScanner results;
//        try {
//            Table table = conn.getTable(TableName.valueOf(tabname));
//            results = table.getScanner(scan);
//            for (Result result : results) {
//                Cell[] rawCells = result.rawCells();
////                for (Cell cell : rawCells) {
////                    //String Qualifier = Bytes.toString(CellUtil.cloneQualifier(cell));
////                    String Value = Bytes.toString(CellUtil.cloneValue(cell));
////                    //String family = Bytes.toString(CellUtil.cloneFamily(cell));
////                    dataList.add(Value);
////                }
//
//                String[] row = new String[rawCells.length];
//                for (int i = 0; i < rawCells.length; i++) {
//                    //String Qualifier = Bytes.toString(CellUtil.cloneQualifier(cell));
//                    String Value = Bytes.toString(CellUtil.cloneValue(rawCells[i]));
//                    //String family = Bytes.toString(CellUtil.cloneFamily(cell));
//                    row[i] = Value;
//                }
//                dataList.add(row);
//            }
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//        return dataList;
//    }


    /**
     * @Description: 获取数据库某个字段非空数据
     * <AUTHOR>
     * @date 2021-03-12
     */
    public static List<String[]> getFieldDataList(Connection conn, String dbname, String tabname, String field, Integer lineNum) {
        List<String[]> dataList = new ArrayList<String[]>();
        Scan scan = new Scan();
        //scan.setMaxResultSize(1);
        if (lineNum != null && lineNum != 0) {
            scan.setMaxResultsPerColumnFamily(lineNum);
        }
        //Connection conn = getConn(url);
        ResultScanner results;
        try {
            Table table = conn.getTable(TableName.valueOf(tabname));
            results = table.getScanner(scan);
            for (Result result : results) {
                Cell[] rawCells = result.rawCells();
                String[] row = new String[rawCells.length];
                for (int i = 0; i < rawCells.length; i++) {
                    String Qualifier = Bytes.toString(CellUtil.cloneQualifier(rawCells[i]));
                    if (field.equals(Qualifier)) {
                        String Value = Bytes.toString(CellUtil.cloneValue(rawCells[i]));
                        if (StringUtils.isNotEmpty(Value)) {
                            row[i] = Value;
                        }
                    }
                }
                dataList.add(row);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return dataList;
    }

    /**
     * @Description: 获取数据库表数据数量
     * <AUTHOR>
     * @date 2020年2月26日
     */
    public static int getTabDataCount(Connection conn, String dbname, String tabname) {
        int i = 0;
        List<String> dataList = new ArrayList<String>();
        Scan scan = new Scan();
        // Connection conn = getConn(url);
        ResultScanner results;
        try {
            Table table = conn.getTable(TableName.valueOf(tabname));

            results = table.getScanner(scan);
            for (Result result : results) {
                Cell[] rawCells = result.rawCells();
                for (Cell cell : rawCells) {
                    i++;
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return i;
    }


    //关闭连接
    public static void close() {
        try {
            if (admins != null) {
                admins.close();
            }
            if (null != connection) {
                connection.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    /**
     * 拿到表信息
     *
     * @param dbName    数据库的名字
     * @param tableName 表名
     * @param conn
     * @return {@code Map<String, String>}
     */
    public static Map<String, String> getTableInfoBySchema(String dbName, String tableName, Connection conn) {
        Map<String, String> tableInfoMap = new HashMap<>();
        tableInfoMap.put("tableName", tableName);
//        closeCon(null, stmt, conn);
        return tableInfoMap;
    }


}
