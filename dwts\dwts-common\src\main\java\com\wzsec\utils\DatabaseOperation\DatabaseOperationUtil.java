package com.wzsec.utils.DatabaseOperation;

import com.wzsec.utils.Const;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.sql.*;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.regex.Pattern;


/**
 * 数据库操作工具类入口
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Slf4j
public abstract class DatabaseOperationUtil {

    public static DatabaseOperationUtil getDatabase(String dbType) {
        switch (dbType) {
            case Const.DB_MYSQL:
                return new MysqlUtils();
            case Const.DB_DM:
                return new DMUtils();
            case Const.DB_POSTGRESQL:
                return new PostgreSQLUtils();
            default:
                throw new IllegalArgumentException("Unsupported database type: " + dbType);
        }
    }

    /**
     * 获取数据库连接
     *
     * @param url      url
     * @param username 用户名
     * @param password 密码
     * @return {@link Connection}
     */
    public abstract Connection getConnection(String url, String username, String password) throws SQLException, ClassNotFoundException;

    /**
     * 获取分页结果集
     *
     * @param conn      连接
     * @param dbName    数据库名称
     * @param tableName 表名称
     * @param condition 条件
     * @param pageSize  页面大小
     * @param StartLine 起始行
     * @param endLine   结束行
     * @return {@link ResultSet}
     * @throws SQLException SQLException
     */
    public abstract List<Map<String, String>> getResultSet(Connection conn, String dbName, String tableName, String condition,
                                                           Long pageSize, long StartLine, long endLine) throws SQLException;

    /**
     * 根据数据库连接获取所有库表
     *
     * @param conn   康纳。
     * @param dbName 数据库名称
     * @return {@link Map}<{@link String}, {@link String}>
     * @throws SQLException SQLException
     */
    public abstract Map<String, String> getAllDbTabMap(Connection conn, String dbName) throws SQLException;

    /**
     * 获取库表信息
     *
     * @param conn      连接
     * @param dbName    数据库名称
     * @param tableName 表名称
     * @return {@link Map}<{@link String}, {@link String}>
     * @throws SQLException SQLException
     */
    public abstract Map<String, String> getTableInfoBySchema(Connection conn, String dbName, String tableName) throws SQLException;


    /**
     * 获取字段列表
     *
     * @param conn      连接
     * @param dbName    数据库名称
     * @param tableName 表名称
     * @return {@link List}<{@link String}>
     * @throws SQLException SQLException
     */
    public abstract List<String> getTabFieldList(Connection conn, String dbName, String tableName) throws SQLException;


    /**
     * 获取表字段信息
     *
     * @param conn      连接
     * @param dbName    数据库名称
     * @param tableName 表名称
     * @return {@link List}<{@link Map}<{@link String}, {@link String}>>
     * @throws SQLException SQLException
     */
    public abstract List<Map<String, String>> getTableFieldInfo(Connection conn, String dbName, String tableName) throws SQLException;


    /**
     * 获取库表总行数
     *
     * @param conn      连接
     * @param dbName    数据库名称
     * @param tableName 表名称
     * @param condition 条件
     * @return int
     * @throws SQLException SQLException
     */
    public abstract int getDataCount(Connection conn, String dbName, String tableName, String condition) throws SQLException;


    /**
     * 获取建表语句
     *
     * @param dbName        数据库名称
     * @param tableName     表名称
     * @param fieldInfoList 字段信息列表
     * @param obj           对象
     * @param maskfields    Maskfields
     * @param srcSql        src-sql (无法获取原库建表语句,该处置为null)
     * @return {@link String}
     */
    public abstract String getCreateTableSql(String dbName, String tableName,
                                             List<Map<String, String>> fieldInfoList,
                                             Map<String, String> obj,
                                             List<String> maskfields, String srcSql);

    /**
     * 获取批量插入信息(key:插入语句; value:插入参数)
     *
     * @param start     开始位
     * @param end       结束位
     * @param dbName    数据库名称
     * @param tableName 表名称
     * @param objList   obj字母
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    public abstract Map<String, Object> getBatchInsertionInformation(int start, int end, String dbName, String tableName,
                                                                     List<Map<String, String>> objList);


    /**
     * 获取选项卡数据列表
     *
     * @param conn      连接
     * @param dbName    数据库库名
     * @param tableName 表名称
     * @param lineNum   查询列数
     * @return {@link List}<{@link String[]}>
     * @throws SQLException SQLException
     */
    public abstract List<String[]> getTabDataList(Connection conn, String dbName, String tableName, Integer lineNum) throws SQLException;


    /**
     * 创建表
     *
     * @param conn      连接
     * @param createSql 创建SQL
     * @return {@link Boolean}
     */
    public static Boolean createTable(Connection conn, String createSql) throws Exception {
        Boolean result = false;
        Statement stmt = null;
        try {
            stmt = conn.createStatement();// 执行创建表
            if (0 == stmt.executeUpdate(createSql)) {
                result = true;
            }
            // } catch (Exception e) {
            //     e.printStackTrace();
            //     result = false;
        } finally {
            closeCon(null, stmt, null);
        }
        return result;
    }

    /**
     * 执行批量插入
     *
     * @param insertSql 插入语句
     * @param params    插入参数
     * @param dbType    库表类型
     * @param dbUrl     URL
     * @param username  用户名
     * @param password  密码
     * @return {@link Boolean}
     */
//    public static Boolean executeInserts(String insertSql, Object[] params, String dbType,
//                                         String dbUrl, String username, String password) throws SQLException, ClassNotFoundException {
//        Boolean result = false;
//        Connection conn = null;
//        PreparedStatement pstmt = null;
//        try {
//            conn = DatabaseOperationUtil.getDatabase(dbType).getConnection(dbUrl, username, password);// 打开连接
//            pstmt = conn.prepareStatement(insertSql);
//            for (int i = 0; i < params.length; i++) {
//                pstmt.setObject(i + 1, params[i]);
//            }
//            if (pstmt.executeUpdate() > 0) {
//                result = true;
//            }
//            // } catch (Exception e) {
//            //     e.printStackTrace();
//            //     result = false;
//        } finally {
//            closeCon(null, pstmt, conn);
//        }
//        return result;
//    }
    /**
     * 执行批量插入
     *
     * @param insertSql 插入语句
     * @param params    插入参数
     * @param dbType    库表类型
     * @param dbUrl     URL
     * @param username  用户名
     * @param password  密码
     * @return {@link Boolean}
     */
    public static Boolean executeInserts(String insertSql, Object[] params, String dbType,
                                         String dbUrl, String username, String password) throws SQLException, ClassNotFoundException {
        Boolean result = false;
        Connection conn = null;
        PreparedStatement pstmt = null;
        try {
            conn = DatabaseOperationUtil.getDatabase(dbType).getConnection(dbUrl, username, password);// 打开连接
            pstmt = conn.prepareStatement(insertSql);
            for (int i = 0; i < params.length; i++) {
                Object param = params[i];
                if (param == null){
                    pstmt.setObject(i + 1, null);
                }else {
                    if (param instanceof String){
                        String strParam = (String) param;
                        if (StringUtils.isEmpty(strParam) || strParam.equals("null")){
                            pstmt.setObject(i + 1, null);
                        }else {
                            try {
                                pstmt.setObject(i + 1, new BigDecimal(strParam));
                            }catch (Exception e){
                                System.out.println("转换BigDecimal失败，转换为String");
                                if (i == 900){
                                    pstmt.setObject(i + 1, strParam);
                                }else {
                                    pstmt.setObject(i + 1, strParam);
                                }
                            }
                        }
                    }else {
                        pstmt.setObject(i + 1, param);
                    }
                }
            }
            if (pstmt.executeUpdate() > 0) {
                result = true;
            }
            // } catch (Exception e) {
            //     e.printStackTrace();
            //     result = false;
        } finally {
            closeCon(null, pstmt, conn);
        }
        return result;
    }


    /**
     * 释放资源
     *
     * @param rs   ResultSet
     * @param st   Statement
     * @param conn Connection
     */
    public static void closeCon(ResultSet rs, Statement st, Connection conn) {
        try {
            if (rs != null) {
                rs.close(); // 关闭结果集
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            try {
                if (st != null) {
                    st.close(); // 关闭Statement
                }
            } catch (SQLException e) {
                e.printStackTrace();
            } finally {
                try {
                    if (conn != null) {
                        conn.close(); // 关闭连接
                    }
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
    }


    /**
     * 限制条件
     *
     * @param in_limitingcondition
     * @return {@link String}
     */
    private static String restrictedCondition(String in_limitingcondition) {
        int limitStart = 0;
        int limitEnd = 10000;
        if (StringUtils.isNotBlank(in_limitingcondition)) {
            // LIMIT
            if (in_limitingcondition.toLowerCase().contains("limit")) {
                String limit = in_limitingcondition.toLowerCase().replaceAll("limit", "").trim();
                if (in_limitingcondition.contains(",")) {
                    String[] split = limit.split(",");
                    limitStart = Integer.parseInt(split[0].trim());
                    limitEnd = Integer.parseInt(split[1].trim());
                    in_limitingcondition = "LIMIT " + limitStart + "," + limitEnd;
                } else {
                    limitEnd = Integer.parseInt(limit.trim());
                    in_limitingcondition = "LIMIT " + limitStart + "," + limitEnd;
                }
            } else {  //WHERE
                in_limitingcondition = "WHERE " + in_limitingcondition;
            }
        } else {
            in_limitingcondition = "";
        }
        return in_limitingcondition;
    }

}
