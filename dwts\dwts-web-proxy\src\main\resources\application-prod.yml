# 生产环境配置
server:
  port: 8080
  tomcat:
    max-threads: 500
    max-connections: 10000

spring:
  datasource:
    url: *******************************************************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:<EMAIL>}
    hikari:
      minimum-idle: 10
      maximum-pool-size: 50
      
  jpa:
    show-sql: false
    hibernate:
      ddl-auto: validate
    properties:
      hibernate:
        format_sql: false

# 日志配置
logging:
  level:
    com.wzsec: INFO
    org.springframework.web: WARN
    org.hibernate.SQL: WARN
  file:
    name: /var/log/dwts-web-proxy/application.log
    max-size: 100MB
    max-history: 30

# 代理配置
web-proxy:
  rest-template:
    connect-timeout: 10000
    read-timeout: 60000
    max-connections: 1000
    max-connections-per-route: 100

# 自定义配置
dwts:
  web-proxy:
    enable-request-logging: false
    enable-response-logging: false
