package com.wzsec.utils;

public class WatermarkUtil {
    /**
     * 字符串转Unicode
     *
     * @param resultTemp 字符串
     * @return {@link String}
     */
    public static String detectHiddenInformation(String resultTemp) {
        StringBuffer unicode = new StringBuffer();

        for (int i = 0; i < resultTemp.length(); ++i) {
            char c = resultTemp.charAt(i);
            unicode.append("\\u" + Integer.toHexString(c));
        }

        StringBuffer unicodeStr = new StringBuffer();
        String unicode2 = unicode.toString();
        String[] splits = unicode2.split("\\\\");

        for (int j = 0; j < splits.length; ++j) {
            if (splits[j].equals("u200b")) {
                unicodeStr.append("0");
            } else if (splits[j].equals("u200c")) {
                unicodeStr.append("1");
            } else if (splits[j].equals("u200d")) {
                unicodeStr.append(" ");
            }
        }

        String infoTo = null;
        if (StringUtils.isNotBlank(unicodeStr.toString())) {
            infoTo = BinaryStringConvertUtil.toString(unicodeStr.toString());
        }

        return infoTo.trim();
    }


    public static String getHiddenInfo(String originalInformation) {
        String name = BinaryStringConvertUtil.toBinaryString(originalInformation);
        String key = "\\u200D";

        for (int j = 0; j < name.length(); ++j) {
            String var4 = String.valueOf(name.charAt(j));
            byte var5 = -1;
            switch (var4.hashCode()) {
                case 48:
                    if (var4.equals("0")) {
                        var5 = 0;
                    }
                    break;
                case 49:
                    if (var4.equals("1")) {
                        var5 = 1;
                    }
            }

            switch (var5) {
                case 0:
                    key = key + "\\u200B";
                    break;
                case 1:
                    key = key + "\\u200C";
                    break;
                default:
                    key = key + "\\u200D";
            }
        }

        return unicode2String(key);
    }

    public static String unicode2String(String unicode) {
        StringBuffer string = new StringBuffer();
        String[] hex = unicode.split("\\\\u");

        for (int i = 1; i < hex.length; ++i) {
            int data = Integer.parseInt(hex[i], 16);
            string.append((char) data);
        }

        return string.toString();
    }

    public static void main(String[] args) {
        String s = getHiddenInfo("武汉市经开区人工智能科技园");
        System.out.println("------------------------");
        System.out.println("表示博愛三十年" + s + "wdscsdfvvv表示博愛三十年嚇死你");
        System.out.println("------------------------");

        String s1 = detectHiddenInformation(s);

        System.out.println(s1);
    }
}
