package com.wzsec.webproxy.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 暗水印配置属性
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Data
@Component
@ConfigurationProperties(prefix = "web-proxy.watermark.invisible")
public class InvisibleWatermarkProperties {

    /**
     * 是否启用暗水印
     */
    private boolean enabled = true;

    /**
     * 编码强度：low, medium, high
     */
    private String encodingStrength = "medium";

    /**
     * 嵌入密度 (0.1-1.0)
     */
    private double embedDensity = 0.3;

    /**
     * 最大嵌入长度
     */
    private int maxEmbedLength = 1000;

    /**
     * 是否启用校验和验证
     */
    private boolean enableChecksum = true;

    /**
     * 水印过期时间（毫秒）
     */
    private long expirationTime = 30L * 24 * 60 * 60 * 1000; // 30天

    /**
     * 最小文本长度（小于此长度的文本不嵌入水印）
     */
    private int minTextLength = 10;

    /**
     * 跳过的字段名称模式
     */
    private String[] skipFieldPatterns = {
        "password", "token", "key", "secret", "auth", "credential"
    };

    /**
     * 获取编码强度对应的参数
     */
    public EncodingStrengthConfig getEncodingConfig() {
        switch (encodingStrength.toLowerCase()) {
            case "low":
                return new EncodingStrengthConfig(2, 0.1, 100);
            case "high":
                return new EncodingStrengthConfig(8, 0.8, 2000);
            case "medium":
            default:
                return new EncodingStrengthConfig(4, 0.3, 1000);
        }
    }

    /**
     * 编码强度配置
     */
    public static class EncodingStrengthConfig {
        private final int bitsPerChar;
        private final double density;
        private final int maxLength;

        public EncodingStrengthConfig(int bitsPerChar, double density, int maxLength) {
            this.bitsPerChar = bitsPerChar;
            this.density = density;
            this.maxLength = maxLength;
        }

        public int getBitsPerChar() { return bitsPerChar; }
        public double getDensity() { return density; }
        public int getMaxLength() { return maxLength; }
    }
}
