package com.wzsec.utils.DatabaseOperation;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.sql.*;
import java.util.*;


@Slf4j
public class PostgreSQLUtils extends DatabaseOperationUtil {

    private static String JDBC_DRIVER = "org.postgresql.Driver";

    @Override
    public Connection getConnection(String url, String username, String password) throws SQLException, ClassNotFoundException {
        Class.forName(JDBC_DRIVER);
        Connection con = DriverManager.getConnection(url, username, password);
        return con;
    }

    @Override
    public List<Map<String, String>> getResultSet(Connection conn, String dbName, String tableName, String condition,
                                                  Long pageSize, long StartLine, long endLine) throws SQLException {

        List<String> tabFieldList = getTabFieldList(conn, dbName, tableName);
        ResultSet rs = null;
        Statement stmt = null;
        String sql = "";

        if (org.apache.commons.lang3.StringUtils.isNotBlank(condition)) {
            String template = "select * from (select * from {} {}) as a limit {} offset {}";
            sql = StrUtil.format(template, tableName, condition, endLine, StartLine);
        } else {
            String template = "select * from {} limit {} offset {}";
            sql = StrUtil.format(template, tableName, endLine, StartLine);
        }
        log.info("查询SQL：" + sql);

        // 将结果集转换为 List<Map>
        List<Map<String, String>> dataList = new ArrayList<>();
        try {
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();

            while (rs.next()) {
                Map<String, String> rowMap = new LinkedHashMap<>(); // 保持插入顺序

                for (String fieldName : tabFieldList) {
                    for (int i = 1; i <= columnCount; i++) {
                        String columnName = metaData.getColumnName(i);
                        if (fieldName.equalsIgnoreCase(columnName)) {
                            Object columnValue = rs.getObject(i);
                            rowMap.put(columnName, String.valueOf(columnValue));
                            break;
                        }
                    }
                }

                dataList.add(rowMap);
            }
        } finally {
            DatabaseOperationUtil.closeCon(rs, stmt, null);
        }
        return dataList;
    }

    @Override
    public Map<String, String> getAllDbTabMap(Connection conn, String dbName) throws SQLException {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Map<String, String> dbTabMap = null;
        try {
            String template = "select tablename from pg_tables where schemaname = '{}'";
            String strSQL = StrUtil.format(template, dbName);
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            dbTabMap = new TreeMap<>();
            while (rs.next()) {
                String table_name = rs.getString(1);
                if (dbTabMap.containsKey(dbName)) {
                    dbTabMap.put(dbName, dbTabMap.get(dbName) + "," + table_name);
                } else {
                    dbTabMap.put(dbName, table_name);
                }
            }
        } finally {
            DatabaseOperationUtil.closeCon(rs, stmt, null);
        }
        return dbTabMap;
    }

    @Override
    public Map<String, String> getTableInfoBySchema(Connection conn, String dbName, String tableName) throws SQLException {
        Map<String, String> tableInfoMap = new HashMap<>();
        Statement stmt = null;
        ResultSet rs = null;
        try {
            stmt = conn.createStatement();// 执行创建表
            String template = "select relname as tabname,cast (obj_description (relfilenode, 'pg_class') as varchar) as comment from pg_class c where relname in ( select tablename from pg_tables where schemaname ='{}'  and position ('_2' in tablename) = 0) AND relname= '{}' ";
            String strSQL = StrUtil.format(template, dbName, tableName);
            rs = stmt.executeQuery(strSQL);
            if (rs != null && rs.next()) {
                if (rs.getString("tabname").equals(tableName)) {
                    tableInfoMap.put("tableName", rs.getString("tabname"));
                    tableInfoMap.put("tableCName", rs.getString("comment"));
                }
            }
        } finally {
            DatabaseOperationUtil.closeCon(rs, stmt, null);
        }
        return tableInfoMap;
    }

    @Override
    public List<String> getTabFieldList(Connection conn, String dbname, String tabname) throws SQLException {
        Statement stmt = null;
        ResultSet rs = null;
        List<String> list = new ArrayList<String>();
        try {
            String template = "SELECT A.attname AS NAME FROM pg_class AS C, pg_attribute AS A WHERE C.relname='{}' AND A.attrelid = C.oid AND A.attnum > 0 ";
            String strSQL = StrUtil.format(template, tabname);
            //String strSQL = "SELECT A.attname AS NAME FROM pg_class AS C, pg_attribute AS A WHERE C.relname='" + tabname + "' AND A.attrelid = C.oid AND A.attnum > 0";
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery(strSQL);
            while (rs.next()) {
                if (!rs.getString(1).contains("......")) {
                    list.add(rs.getString(1));
                }
            }
        } finally {
            DatabaseOperationUtil.closeCon(rs, stmt, null);
        }
        return list;
    }

    @Override
    public List<Map<String, String>> getTableFieldInfo(Connection conn, String dbName, String tableName) throws SQLException {
        List<Map<String, String>> fieldInfoList = null;
        Statement stmt = null;
        ResultSet rs = null;
        try {
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery("Select a.attnum,(select description from pg_catalog.pg_description where objoid=a.attrelid and objsubid=a.attnum) as descript ,a.attname,pg_catalog.format_type(a.atttypid,a.atttypmod) as data_type from pg_catalog.pg_attribute a where 1=1 and a.attrelid=(select oid from pg_class where relname='" + tableName + "' ) and a.attnum>0 and not a.attisdropped order by a.attnum;");
            if (rs != null) {
                fieldInfoList = new ArrayList<>();
                while (rs.next()) {
                    Map<String, String> fieldInfoMap = new HashMap<>();
                    fieldInfoMap.put("Field", rs.getString("attname"));
                    fieldInfoMap.put("Type", rs.getString("data_type"));
                    fieldInfoMap.put("description", rs.getString("descript"));
                    fieldInfoList.add(fieldInfoMap);
                }
            }
        } finally {
            DatabaseOperationUtil.closeCon(rs, stmt, null);
        }
        return fieldInfoList;
    }

    @Override
    public int getDataCount(Connection conn, String in_dbname, String in_tabname, String in_limitingcondition) throws SQLException {
        log.info("连接postgresql数据库成功");
        // PostgreSQL limit语法顺序
        int count = 0;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        String sql = "";
        if (StringUtils.isNotBlank(in_limitingcondition)) {
            if (in_limitingcondition.toLowerCase().contains("limit")) {
                String limit = in_limitingcondition.toLowerCase().replaceAll("limit", "").trim();
                String[] split = limit.split(",");
                if (split.length > 1) {
                    String template = " limit {} offset {} ";
                    in_limitingcondition = StrUtil.format(template, split[1], split[0]);
                } else {
                    in_limitingcondition = "";
                }
            }
            String template = "select count(*) from (select * from {} {}) as a ";
            sql = StrUtil.format(template, in_tabname, in_limitingcondition);
        } else {
            String template = "select count(*) from {} ";
            sql = StrUtil.format(template, in_tabname);
        }
        log.info("查询SQL：" + sql);
        preparedStatement = conn.prepareStatement(sql);
        resultSet = preparedStatement.executeQuery();
        try {
            preparedStatement = conn.prepareStatement(sql);
            resultSet = preparedStatement.executeQuery();

            while (resultSet.next()) {
                count = (int) resultSet.getLong(1);
            }
        } finally {
            DatabaseOperationUtil.closeCon(resultSet, preparedStatement, null);
        }
        return count;
    }

    @Override
    public String getCreateTableSql(String dbName, String tableName,
                                    List<Map<String, String>> fieldInfoList,
                                    Map<String, String> obj,
                                    List<String> maskfields,
                                    String srcSql) {
        String sql = null;
        try {
            StringBuilder sb = new StringBuilder();
            sb.append("CREATE TABLE ").append(tableName).append(" (\r\n");
            for (Map<String, String> fieldInfo : fieldInfoList) {
                if (!obj.keySet().contains(fieldInfo.get("Field"))) {// 跳过没有抽取的列
                    continue;
                }
                sb.append("\"" + fieldInfo.get("Field") + "\"");// 字段名
                if (maskfields != null && maskfields.contains(fieldInfo.get("Field"))) {// 加解密的字段类型更改为varchar
                    sb.append(" varchar(255)");// 类型
                } else {
                    sb.append(" varchar(255)");// 类型
                }
                sb.append(",\n");
            }
            sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sql = sql + "\r)";
        } catch (Exception e) {
            e.printStackTrace();
            sql = null;
        }
        return sql;
    }

    @Override
    public Map<String, Object> getBatchInsertionInformation(int start, int end, String dbname, String tableName, List<Map<String, String>> objList) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO ").append(tableName).append(" (");
            for (String column : fields) {
                sb.append("\"" + column + "\"").append(", ");
            }
            String sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sb = new StringBuilder(sql);
            sb.append(") VALUES ");
            for (int i = start; i < end; i++) {
                Map<String, String> map = objList.get(i);
                sb.append("(");
                for (String key : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?, ");
                    params.add(map.get(key));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append("), ");
            }
            sql = sb.toString();
            lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
// sql += ";";
            sqlAndParams = new HashMap<>();
            sqlAndParams.put("sql", sql);
            sqlAndParams.put("params", params.toArray());
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    @Override
    public List<String[]> getTabDataList(Connection conn, String dbname, String tableName, Integer lineNum) throws SQLException {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<String[]> tabDataList = new ArrayList<String[]>();
        String strSQL = null;
        try {
            String template = "select * from {}.{}.{} ";
            strSQL = StrUtil.format(template, dbname,"public", tableName);
            System.out.println("实际执行的查询语句: " + strSQL);
            //strSQL = "select * from " + tabname;
            if (lineNum != null && lineNum != 0)
                strSQL += " LIMIT " + lineNum;
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            ResultSetMetaData md = rs.getMetaData(); //获得结果集结构信息,元数据
            int columnCount = md.getColumnCount();   //获得列数
            while (rs.next()) {
                String[] row = new String[columnCount];
                for (int i = 0; i < columnCount; i++) {
                    row[i] = rs.getString(i + 1) == null ? "" : rs.getString(i + 1);
                }
                tabDataList.add(row);
            }
        } finally {
            DatabaseOperationUtil.closeCon(rs, stmt, null);
        }
        return tabDataList;
    }
}
