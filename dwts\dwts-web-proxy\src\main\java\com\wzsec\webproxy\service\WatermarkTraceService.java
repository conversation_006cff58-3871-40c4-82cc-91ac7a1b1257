package com.wzsec.webproxy.service;

import com.wzsec.webproxy.domain.WebProxyRecord;
import com.wzsec.webproxy.repository.WebProxyRecordRepository;
import com.wzsec.webproxy.watermark.util.WatermarkExtractor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 水印溯源服务
 * 提供水印提取、溯源分析和泄露追踪功能
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Slf4j
@Service
public class WatermarkTraceService {

    @Autowired
    private WatermarkExtractor watermarkExtractor;

    @Autowired
    private WebProxyRecordRepository recordRepository;

    /**
     * 分析可疑内容并生成溯源报告
     *
     * @param suspiciousContent 可疑泄露的内容
     * @param contentType 内容类型
     * @return 溯源分析结果
     */
    public TraceAnalysisResult analyzeSuspiciousContent(String suspiciousContent, String contentType) {
        log.info("开始分析可疑内容，内容长度: {}, 类型: {}", suspiciousContent.length(), contentType);

        TraceAnalysisResult result = new TraceAnalysisResult();
        result.setAnalysisTime(System.currentTimeMillis());
        result.setContentLength(suspiciousContent.length());
        result.setContentType(contentType);

        try {
            // 1. 提取水印信息
            List<WatermarkExtractor.ExtractedWatermark> watermarks =
                    watermarkExtractor.extractWatermarks(suspiciousContent, contentType);

            if (watermarks.isEmpty()) {
                result.setHasWatermark(false);
                result.setRiskLevel(RiskLevel.UNKNOWN);
                result.setMessage("未检测到水印信息，无法进行溯源分析");
                return result;
            }

            result.setHasWatermark(true);
            result.setWatermarkCount(watermarks.size());

            // 2. 生成溯源报告
            WatermarkExtractor.TraceReport traceReport = watermarkExtractor.generateTraceReport(watermarks);
            result.setTraceReport(traceReport);

            // 3. 分析风险等级
            RiskLevel riskLevel = assessRiskLevel(watermarks, traceReport);
            result.setRiskLevel(riskLevel);

            // 4. 查找相关访问记录
            List<AccessRecord> relatedRecords = findRelatedAccessRecords(watermarks);
            result.setRelatedAccessRecords(relatedRecords);

            // 5. 生成溯源建议
            List<String> recommendations = generateRecommendations(watermarks, traceReport, riskLevel);
            result.setRecommendations(recommendations);

            // 6. 计算可信度分数
            double confidenceScore = calculateConfidenceScore(watermarks, relatedRecords);
            result.setConfidenceScore(confidenceScore);

            result.setMessage(String.format("成功提取 %d 个水印，风险等级: %s，可信度: %.2f",
                    watermarks.size(), riskLevel, confidenceScore));

            log.info("溯源分析完成 - 水印数量: {}, 风险等级: {}, 可信度: {}",
                    watermarks.size(), riskLevel, confidenceScore);

        } catch (Exception e) {
            log.error("溯源分析失败", e);
            result.setHasWatermark(false);
            result.setRiskLevel(RiskLevel.ERROR);
            result.setMessage("溯源分析过程中发生错误: " + e.getMessage());
        }

        return result;
    }

    /**
     * 评估风险等级
     */
    private RiskLevel assessRiskLevel(List<WatermarkExtractor.ExtractedWatermark> watermarks,
                                      WatermarkExtractor.TraceReport traceReport) {

        // 基于多个因素评估风险
        int riskScore = 0;

        // 1. 水印数量因素
        if (watermarks.size() > 10) {
            riskScore += 3; // 大量水印表示可能是批量泄露
        } else if (watermarks.size() > 5) {
            riskScore += 2;
        } else if (watermarks.size() > 1) {
            riskScore += 1;
        }

        // 2. 用户多样性因素
        int uniqueUsers = traceReport.getUserStatistics().size();
        if (uniqueUsers > 5) {
            riskScore += 3; // 多用户涉及表示影响范围大
        } else if (uniqueUsers > 2) {
            riskScore += 2;
        } else if (uniqueUsers > 1) {
            riskScore += 1;
        }

        // 3. IP多样性因素
        int uniqueIPs = traceReport.getIpStatistics().size();
        if (uniqueIPs > 3) {
            riskScore += 2; // 多IP表示可能跨地域泄露
        } else if (uniqueIPs > 1) {
            riskScore += 1;
        }

        // 4. 时间跨度因素
        long timeSpan = calculateTimeSpan(watermarks);
        if (timeSpan > 7 * 24 * 60 * 60 * 1000L) { // 超过7天
            riskScore += 2;
        } else if (timeSpan > 24 * 60 * 60 * 1000L) { // 超过1天
            riskScore += 1;
        }

        // 根据总分确定风险等级
        if (riskScore >= 8) {
            return RiskLevel.CRITICAL;
        } else if (riskScore >= 6) {
            return RiskLevel.HIGH;
        } else if (riskScore >= 4) {
            return RiskLevel.MEDIUM;
        } else if (riskScore >= 2) {
            return RiskLevel.LOW;
        } else {
            return RiskLevel.MINIMAL;
        }
    }

    /**
     * 计算时间跨度
     */
    private long calculateTimeSpan(List<WatermarkExtractor.ExtractedWatermark> watermarks) {
        if (watermarks.isEmpty()) {
            return 0;
        }

        long minTime = watermarks.stream()
                .mapToLong(w -> w.getWatermarkInfo().getTimestamp())
                .min()
                .orElse(0);

        long maxTime = watermarks.stream()
                .mapToLong(w -> w.getWatermarkInfo().getTimestamp())
                .max()
                .orElse(0);

        return maxTime - minTime;
    }

    /**
     * 查找相关访问记录
     */
    private List<AccessRecord> findRelatedAccessRecords(List<WatermarkExtractor.ExtractedWatermark> watermarks) {
        List<AccessRecord> records = new ArrayList<>();

        try {
            // 提取所有相关的IP和时间范围
            Set<String> ipAddresses = watermarks.stream()
                    .map(w -> w.getWatermarkInfo().getIpAddress())
                    .collect(Collectors.toSet());

            long minTime = watermarks.stream()
                    .mapToLong(w -> w.getWatermarkInfo().getTimestamp())
                    .min()
                    .orElse(System.currentTimeMillis() - 24 * 60 * 60 * 1000L);

            long maxTime = watermarks.stream()
                    .mapToLong(w -> w.getWatermarkInfo().getTimestamp())
                    .max()
                    .orElse(System.currentTimeMillis());

            // 扩展时间范围（前后各1小时）
            Date startTime = new Date(minTime - 60 * 60 * 1000L);
            Date endTime = new Date(maxTime + 60 * 60 * 1000L);

            // 查询数据库中的相关记录
            for (String ip : ipAddresses) {
                List<WebProxyRecord> proxyRecords = recordRepository
                        .findByRequestIpAndCreateTimeBetween(ip, new Timestamp(startTime.getTime()), new Timestamp(endTime.getTime()));

                for (WebProxyRecord record : proxyRecords) {
                    AccessRecord accessRecord = new AccessRecord();
                    accessRecord.setIpAddress(record.getRequestIp());
                    accessRecord.setRequestTime(record.getCreateTime().getTime());
                    accessRecord.setRequestPath(record.getRequestPath());
                    accessRecord.setUserAgent(record.getUserAgent());
                    accessRecord.setResponseSize(record.getResponseSize());
                    accessRecord.setProxyName("proxy-" + record.getProxyConfigId()); // 使用配置ID作为代理名

                    records.add(accessRecord);
                }
            }

        } catch (Exception e) {
            log.warn("查找相关访问记录失败", e);
        }

        return records;
    }

    /**
     * 生成溯源建议
     */
    private List<String> generateRecommendations(List<WatermarkExtractor.ExtractedWatermark> watermarks,
                                                 WatermarkExtractor.TraceReport traceReport,
                                                 RiskLevel riskLevel) {
        List<String> recommendations = new ArrayList<>();

        // 基于风险等级的建议
        switch (riskLevel) {
            case CRITICAL:
                recommendations.add("立即启动安全事件响应流程");
                recommendations.add("通知相关用户并要求更改密码");
                recommendations.add("审查所有涉及用户的访问权限");
                recommendations.add("考虑临时禁用相关账户");
                break;

            case HIGH:
                recommendations.add("启动内部安全调查");
                recommendations.add("通知涉及的用户和管理员");
                recommendations.add("加强对相关IP地址的监控");
                break;

            case MEDIUM:
                recommendations.add("记录此次事件并持续监控");
                recommendations.add("通知相关用户注意数据安全");
                recommendations.add("检查访问日志是否有异常");
                break;

            case LOW:
                recommendations.add("记录此次检测结果");
                recommendations.add("定期检查类似情况");
                break;

            default:
                recommendations.add("保持正常监控");
        }

        // 基于用户数量的建议
        if (traceReport.getUserStatistics().size() > 1) {
            recommendations.add("调查多用户涉及的原因");
            recommendations.add("检查是否存在账户共享情况");
        }

        // 基于IP数量的建议
        if (traceReport.getIpStatistics().size() > 1) {
            recommendations.add("分析不同IP地址的地理位置");
            recommendations.add("检查是否存在异常登录行为");
        }

        return recommendations;
    }

    /**
     * 计算可信度分数
     */
    private double calculateConfidenceScore(List<WatermarkExtractor.ExtractedWatermark> watermarks,
                                            List<AccessRecord> relatedRecords) {
        double score = 0.0;

        // 基础分数：有水印就有基础可信度
        if (!watermarks.isEmpty()) {
            score += 0.3;
        }

        // 水印数量因素
        score += Math.min(watermarks.size() * 0.1, 0.3);

        // 访问记录匹配因素
        if (!relatedRecords.isEmpty()) {
            score += 0.2;

            // 记录数量越多，可信度越高
            score += Math.min(relatedRecords.size() * 0.02, 0.2);
        }

        // 确保分数在0-1之间
        return Math.min(Math.max(score, 0.0), 1.0);
    }

    /**
     * 风险等级枚举
     */
    public enum RiskLevel {
        UNKNOWN("未知"),
        MINIMAL("极低"),
        LOW("低"),
        MEDIUM("中"),
        HIGH("高"),
        CRITICAL("严重"),
        ERROR("错误");

        private final String description;

        RiskLevel(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 溯源分析结果
     */
    public static class TraceAnalysisResult {
        private long analysisTime;
        private boolean hasWatermark;
        private int watermarkCount;
        private int contentLength;
        private String contentType;
        private RiskLevel riskLevel;
        private double confidenceScore;
        private String message;
        private WatermarkExtractor.TraceReport traceReport;
        private List<AccessRecord> relatedAccessRecords;
        private List<String> recommendations;

        // Getters and Setters
        public long getAnalysisTime() {
            return analysisTime;
        }

        public void setAnalysisTime(long analysisTime) {
            this.analysisTime = analysisTime;
        }

        public boolean isHasWatermark() {
            return hasWatermark;
        }

        public void setHasWatermark(boolean hasWatermark) {
            this.hasWatermark = hasWatermark;
        }

        public int getWatermarkCount() {
            return watermarkCount;
        }

        public void setWatermarkCount(int watermarkCount) {
            this.watermarkCount = watermarkCount;
        }

        public int getContentLength() {
            return contentLength;
        }

        public void setContentLength(int contentLength) {
            this.contentLength = contentLength;
        }

        public String getContentType() {
            return contentType;
        }

        public void setContentType(String contentType) {
            this.contentType = contentType;
        }

        public RiskLevel getRiskLevel() {
            return riskLevel;
        }

        public void setRiskLevel(RiskLevel riskLevel) {
            this.riskLevel = riskLevel;
        }

        public double getConfidenceScore() {
            return confidenceScore;
        }

        public void setConfidenceScore(double confidenceScore) {
            this.confidenceScore = confidenceScore;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public WatermarkExtractor.TraceReport getTraceReport() {
            return traceReport;
        }

        public void setTraceReport(WatermarkExtractor.TraceReport traceReport) {
            this.traceReport = traceReport;
        }

        public List<AccessRecord> getRelatedAccessRecords() {
            return relatedAccessRecords;
        }

        public void setRelatedAccessRecords(List<AccessRecord> relatedAccessRecords) {
            this.relatedAccessRecords = relatedAccessRecords;
        }

        public List<String> getRecommendations() {
            return recommendations;
        }

        public void setRecommendations(List<String> recommendations) {
            this.recommendations = recommendations;
        }
    }

    /**
     * 访问记录
     */
    public static class AccessRecord {
        private String ipAddress;
        private long requestTime;
        private String requestPath;
        private String userAgent;
        private Long responseSize;
        private String proxyName;

        // Getters and Setters
        public String getIpAddress() {
            return ipAddress;
        }

        public void setIpAddress(String ipAddress) {
            this.ipAddress = ipAddress;
        }

        public long getRequestTime() {
            return requestTime;
        }

        public void setRequestTime(long requestTime) {
            this.requestTime = requestTime;
        }

        public String getRequestPath() {
            return requestPath;
        }

        public void setRequestPath(String requestPath) {
            this.requestPath = requestPath;
        }

        public String getUserAgent() {
            return userAgent;
        }

        public void setUserAgent(String userAgent) {
            this.userAgent = userAgent;
        }

        public Long getResponseSize() {
            return responseSize;
        }

        public void setResponseSize(Long responseSize) {
            this.responseSize = responseSize;
        }

        public String getProxyName() {
            return proxyName;
        }

        public void setProxyName(String proxyName) {
            this.proxyName = proxyName;
        }
    }
}
