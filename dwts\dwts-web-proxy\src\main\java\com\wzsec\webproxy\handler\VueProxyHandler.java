package com.wzsec.webproxy.handler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.regex.Pattern;

/**
 * Vue应用代理处理器
 * 处理Vue单页应用的特殊需求
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Slf4j
@Component
public class VueProxyHandler {

    /**
     * 代理协议配置
     * 可选值: "http", "https", "auto"
     * - "http": 强制使用HTTP协议
     * - "https": 强制使用HTTPS协议
     * - "auto": 根据原始请求协议自动选择
     */
    private static final String PROXY_PROTOCOL = "https";

    // Vue应用常见的API路径模式
    private static final Pattern[] API_PATTERNS = {
        Pattern.compile("^/api/.*"),
        Pattern.compile("^/rest/.*"),
        Pattern.compile("^/service/.*"),
        Pattern.compile("^/v\\d+/.*"),
        Pattern.compile(".*\\.json$"),
        Pattern.compile(".*\\.do$"),
        Pattern.compile(".*\\.action$")
    };

    // Vue应用静态资源模式
    private static final Pattern[] STATIC_PATTERNS = {
        Pattern.compile(".*\\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$"),
        Pattern.compile("^/static/.*"),
        Pattern.compile("^/assets/.*"),
        Pattern.compile("^/img/.*"),
        Pattern.compile("^/css/.*"),
        Pattern.compile("^/js/.*")
    };

    /**
     * 判断是否是API请求
     */
    public boolean isApiRequest(String path) {
        if (path == null) {
            return false;
        }
        
        for (Pattern pattern : API_PATTERNS) {
            if (pattern.matcher(path).matches()) {
                log.debug("识别为API请求: {}", path);
                return true;
            }
        }
        
        return false;
    }

    /**
     * 判断是否是静态资源请求
     */
    public boolean isStaticResource(String path) {
        if (path == null) {
            return false;
        }
        
        for (Pattern pattern : STATIC_PATTERNS) {
            if (pattern.matcher(path).matches()) {
                log.debug("识别为静态资源: {}", path);
                return true;
            }
        }
        
        return false;
    }

    /**
     * 判断是否是Vue页面请求
     */
    public boolean isVuePageRequest(String path, String contentType) {
        // 如果是静态资源或API，则不是页面请求
        if (isStaticResource(path) || isApiRequest(path)) {
            return false;
        }
        
        // 如果Content-Type是HTML，则是页面请求
        if (contentType != null && contentType.toLowerCase().contains("text/html")) {
            return true;
        }
        
        // 如果路径看起来像页面路径
        return path == null || path.equals("/") || !path.contains(".");
    }

    /**
     * 处理Vue应用的HTML内容
     * 主要是重写资源路径和API路径
     */
    public String processVueContent(String content, HttpServletRequest request, String targetHost, int targetPort) {
        if (content == null || content.trim().isEmpty()) {
            return content;
        }

        // 根据配置确定代理协议
        String proxyProtocol = determineProxyProtocol(request);
        String proxyHost = request.getServerName() + ":" + request.getLocalPort();
        String proxyBaseUrl = proxyProtocol + "://" + proxyHost;
        String targetBaseUrl = "http://" + targetHost + ":" + targetPort;

        log.info("处理Vue内容 - 代理基础URL: {} (协议配置: {}), 目标: {}", proxyBaseUrl, PROXY_PROTOCOL, targetBaseUrl);

        // 替换绝对路径为代理路径
        content = content.replaceAll("https?://" + Pattern.quote(targetHost + ":" + targetPort),
                proxyBaseUrl);

        // 处理相对路径的资源引用
        content = content.replaceAll("(src|href)=\"/([^\"]*?)\"",
                "$1=\"" + proxyBaseUrl + "/$2\"");

        // 处理CSS中的url()引用
        content = content.replaceAll("url\\(['\"]?/([^'\"\\)]*)['\"]?\\)",
                "url('" + proxyBaseUrl + "/$1')");
        
        // 处理JavaScript中的API调用
        content = processJavaScriptApiCalls(content, proxyHost);
        
        // 处理Vue Router的base路径
        content = content.replaceAll("(base:\\s*['\"])/([^'\"]*)['\"]", 
                "$1http://" + proxyHost + "/$2\"");
        
        // 处理axios baseURL配置
        content = content.replaceAll("(baseURL:\\s*['\"])/([^'\"]*)['\"]", 
                "$1http://" + proxyHost + "/$2\"");
        
        log.debug("Vue内容处理完成，内容长度: {}", content.length());
        
        return content;
    }

    /**
     * 处理JavaScript中的API调用
     */
    private String processJavaScriptApiCalls(String content, String proxyHost) {
        // 处理fetch调用 - 所有以/开头的路径
        content = content.replaceAll("fetch\\(['\"]/(\\w[^'\"]*)['\"]",
                "fetch('http://" + proxyHost + "/$1'");

        // 处理axios调用 - 所有以/开头的路径
        content = content.replaceAll("axios\\.(get|post|put|delete|patch|request)\\(['\"]/(\\w[^'\"]*)['\"]",
                "axios.$1('http://" + proxyHost + "/$2'");

        // 处理axios配置对象中的url
        content = content.replaceAll("(url:\\s*['\"])/([^'\"]*)['\"]",
                "$1http://" + proxyHost + "/$2\"");

        // 处理$.ajax调用
        content = content.replaceAll("(\\$\\.ajax\\([^}]*url:\\s*['\"])/([^'\"]*)['\"]",
                "$1http://" + proxyHost + "/$2\"");

        // 处理XMLHttpRequest
        content = content.replaceAll("(open\\([^,]*,\\s*['\"])/([^'\"]*)['\"]",
                "$1http://" + proxyHost + "/$2\"");

        // 处理request库调用
        content = content.replaceAll("(request\\([^}]*url:\\s*['\"])/([^'\"]*)['\"]",
                "$1http://" + proxyHost + "/$2\"");

        // 处理Vue中的$http调用
        content = content.replaceAll("(\\$http\\.(get|post|put|delete|patch)\\(['\"])/([^'\"]*)['\"]",
                "$1http://" + proxyHost + "/$3'");

        return content;
    }

    /**
     * 判断是否需要Vue特殊处理
     */
    public boolean needsVueProcessing(String targetHost, String path, String contentType) {
        // 如果目标主机包含常见的Vue应用端口或路径特征
        boolean isVueApp = targetHost != null && 
                          (targetHost.contains(":3000") || 
                           targetHost.contains(":8080") || 
                           targetHost.contains(":8081") || 
                           targetHost.contains(":8082") ||
                           targetHost.contains("vue") ||
                           targetHost.contains("frontend"));
        
        // 如果是HTML内容且可能是Vue应用
        boolean isHtmlContent = contentType != null && contentType.toLowerCase().contains("text/html");
        
        return isVueApp && isHtmlContent && isVuePageRequest(path, contentType);
    }

    /**
     * 根据配置确定代理协议
     */
    private String determineProxyProtocol(HttpServletRequest request) {
        switch (PROXY_PROTOCOL.toLowerCase()) {
            case "http":
                return "http";
            case "https":
                return "https";
            case "auto":
                return request.isSecure() ? "https" : "http";
            default:
                log.warn("未知的代理协议配置: {}, 使用默认HTTPS", PROXY_PROTOCOL);
                return "https";
        }
    }
}
