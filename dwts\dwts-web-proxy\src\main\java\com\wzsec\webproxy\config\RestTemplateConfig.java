package com.wzsec.webproxy.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.ssl.SSLContextBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;


/**
 * Rest模板配置
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Slf4j
@Configuration
public class RestTemplateConfig {

    @Value("${web-proxy.rest-template.connect-timeout:10000}")
    private int connectTimeout;

    @Value("${web-proxy.rest-template.read-timeout:60000}")
    private int readTimeout;

    @Value("${web-proxy.rest-template.max-connections:500}")
    private int maxConnections;

    @Value("${web-proxy.rest-template.max-connections-per-route:50}")
    private int maxConnectionsPerRoute;

    @Bean
    public RestTemplate restTemplate() throws Exception {
        // 创建信任所有证书的SSL上下文
        SSLContext sslContext = SSLContextBuilder.create()
                .loadTrustMaterial((certificate, authType) -> true)
                .build();

        // 创建连接socket工厂注册表
        Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory>create()
                .register("http", PlainConnectionSocketFactory.getSocketFactory())
                .register("https", new SSLConnectionSocketFactory(sslContext, NoopHostnameVerifier.INSTANCE))
                .build();

        // 创建连接池管理器
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager(socketFactoryRegistry);
        connectionManager.setMaxTotal(maxConnections);
        connectionManager.setDefaultMaxPerRoute(maxConnectionsPerRoute);

        // 创建请求配置
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(connectTimeout)
                .setSocketTimeout(readTimeout)
                .setConnectionRequestTimeout(connectTimeout)
                .setRedirectsEnabled(true)
                .setMaxRedirects(10)
                .setContentCompressionEnabled(false)  // 禁用内容压缩，避免编码问题
                .build();

        // 创建HttpClient
        HttpClient httpClient = HttpClientBuilder.create()
                .setConnectionManager(connectionManager)
                .setDefaultRequestConfig(requestConfig)
                .setSSLContext(sslContext)
                .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                .build();

        // 创建请求工厂
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory(httpClient);

        // 创建RestTemplate
        RestTemplate restTemplate = new RestTemplate(factory);

        // 添加错误处理器
        restTemplate.setErrorHandler(new org.springframework.web.client.ResponseErrorHandler() {
            @Override
            public boolean hasError(org.springframework.http.client.ClientHttpResponse response) throws java.io.IOException {
                return response.getStatusCode().series() == org.springframework.http.HttpStatus.Series.SERVER_ERROR;
            }

            @Override
            public void handleError(org.springframework.http.client.ClientHttpResponse response) throws java.io.IOException {
                log.warn("目标服务器返回错误状态: {} {}", response.getStatusCode(), response.getStatusText());
                // 不抛出异常，让代理继续处理
            }
        });

        log.info("RestTemplate配置完成 - 连接超时: {}ms, 读取超时: {}ms, 最大连接数: {}, 每路由最大连接数: {}, HTTPS支持: 已启用",
                connectTimeout, readTimeout, maxConnections, maxConnectionsPerRoute);

        return restTemplate;
    }
}
