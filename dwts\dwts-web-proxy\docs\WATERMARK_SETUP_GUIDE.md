# API 暗水印系统配置和操作指南

## 🚀 快速开始

### 1. 数据库配置

#### 执行数据库迁移
```sql
-- 连接到数据库
mysql -u username -p database_name

-- 执行迁移脚本
source dwts-web-proxy/src/main/resources/db/migration/V1.1__Add_Invisible_Watermark_Fields.sql
```

#### 验证表结构
```sql
-- 检查 dwts_web_proxy_config 表是否添加了新字段
DESCRIBE dwts_web_proxy_config;

-- 检查新创建的溯源表
DESCRIBE watermark_trace_record;
DESCRIBE watermark_extract_detail;
```

### 2. 应用配置

#### 修改 application.yml
```yaml
web-proxy:
  watermark:
    default-text: "DWTS水印-{IP}-{TIME}"
    invisible:
      enabled: true
      encoding-strength: medium  # low, medium, high
      embed-density: 0.3  # 0.1-1.0
      max-embed-length: 1000
```

#### 启动应用
```bash
cd dwts/dwts-web-proxy
mvn clean compile
mvn spring-boot:run
```

### 3. 验证安装

```bash
# 检查应用是否启动成功
curl http://localhost:9090/actuator/health

# 检查水印配置API
curl http://localhost:9090/api/watermark/config/list
```

## ⚙️ 配置管理

### 1. 查看当前配置

```bash
# 获取所有代理配置
curl -X GET "http://localhost:9090/api/watermark/config/list" \
  -H "Accept: application/json"

# 响应示例
{
  "success": true,
  "data": [
    {
      "id": 1,
      "proxyName": "test-proxy",
      "enablePageWatermark": true,
      "enableApiWatermark": true,
      "enableInvisibleWatermark": true,
      "invisibleEncodingStrength": "medium",
      "invisibleEmbedDensity": 0.3
    }
  ],
  "total": 1,
  "message": "获取配置列表成功"
}
```

### 2. 启用暗水印功能

```bash
# 为指定配置启用暗水印
curl -X PUT "http://localhost:9090/api/watermark/config/invisible/1" \
  -H "Content-Type: application/json" \
  -d '{
    "enableInvisible": true,
    "encodingStrength": "medium",
    "embedDensity": 0.5
  }'
```

### 3. 批量配置

```bash
# 批量启用暗水印
curl -X PUT "http://localhost:9090/api/watermark/config/invisible/batch" \
  -H "Content-Type: application/json" \
  -d '{
    "configIds": [1, 2, 3],
    "enabled": true
  }'
```

### 4. 获取推荐配置

```bash
# 获取高安全级别的推荐配置
curl -X GET "http://localhost:9090/api/watermark/config/recommended?securityLevel=high"

# 响应示例
{
  "success": true,
  "data": {
    "enablePageWatermark": true,
    "enableApiWatermark": true,
    "enableInvisibleWatermark": true,
    "invisibleEncodingStrength": "high",
    "invisibleEmbedDensity": 0.8,
    "description": "高安全级别：启用所有水印功能，最大化溯源能力"
  },
  "securityLevel": "high",
  "message": "获取推荐配置成功"
}
```

## 🔍 溯源操作

### 1. 分析可疑内容

```bash
# 分析包含暗水印的JSON内容
curl -X POST "http://localhost:9090/api/watermark/trace/analyze" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "{\"data\": \"这里是包含暗水印的内容\u200B\u200C\u200D\"}",
    "contentType": "application/json"
  }'

# 响应示例
{
  "success": true,
  "data": {
    "hasWatermark": true,
    "watermarkCount": 1,
    "riskLevel": "LOW",
    "confidenceScore": 0.85,
    "traceReport": {
      "watermarkCount": 1,
      "userStatistics": {"user123": 1},
      "ipStatistics": {"*************": 1},
      "uniqueSessionCount": 1
    },
    "recommendations": [
      "记录此次检测结果",
      "定期检查类似情况"
    ]
  }
}
```

### 2. 文件水印提取

```bash
# 从文件中提取水印
curl -X POST "http://localhost:9090/api/watermark/trace/extract-from-file" \
  -F "file=@suspicious_data.json" \
  -F "contentType=application/json"
```

### 3. 快速检测

```bash
# 快速检测是否包含水印
curl -X POST "http://localhost:9090/api/watermark/trace/quick-detect" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "待检测的内容",
    "contentType": "text/plain"
  }'
```

## 📊 监控和统计

### 1. 获取配置统计

```bash
# 获取水印配置统计信息
curl -X GET "http://localhost:9090/api/watermark/config/statistics"

# 响应示例
{
  "success": true,
  "data": {
    "totalConfigs": 5,
    "pageWatermarkEnabled": 4,
    "apiWatermarkEnabled": 5,
    "invisibleWatermarkEnabled": 3,
    "strengthDistribution": {
      "low": 1,
      "medium": 2,
      "high": 0
    }
  }
}
```

### 2. 监控脚本

创建监控脚本 `monitor_watermark.sh`：

```bash
#!/bin/bash

API_BASE="http://localhost:9090/api/watermark"
LOG_FILE="/var/log/watermark_monitor.log"

# 记录日志函数
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> $LOG_FILE
}

# 检查配置状态
check_config_status() {
    response=$(curl -s "$API_BASE/config/statistics")
    
    if [ $? -eq 0 ]; then
        enabled_count=$(echo $response | jq -r '.data.invisibleWatermarkEnabled // 0')
        total_count=$(echo $response | jq -r '.data.totalConfigs // 0')
        
        log_message "配置检查: 暗水印启用 $enabled_count/$total_count"
        
        if [ "$enabled_count" -lt "$total_count" ]; then
            log_message "警告: 部分代理未启用暗水印功能"
        fi
    else
        log_message "错误: 无法获取配置统计信息"
    fi
}

# 检查应用健康状态
check_health() {
    response=$(curl -s "http://localhost:9090/actuator/health")
    
    if [ $? -eq 0 ]; then
        status=$(echo $response | jq -r '.status // "UNKNOWN"')
        log_message "健康检查: $status"
    else
        log_message "错误: 应用健康检查失败"
    fi
}

# 执行检查
log_message "开始监控检查"
check_health
check_config_status
log_message "监控检查完成"
```

### 3. 设置定时任务

```bash
# 添加执行权限
chmod +x monitor_watermark.sh

# 编辑 crontab
crontab -e

# 添加定时任务（每小时执行一次）
0 * * * * /path/to/monitor_watermark.sh

# 每天清理30天前的日志
0 2 * * * find /var/log -name "watermark_monitor.log" -mtime +30 -delete
```

## 🛠️ 故障排查

### 1. 常见问题诊断

#### 问题1: 暗水印未生效
```bash
# 检查配置是否正确
curl -X GET "http://localhost:9090/api/watermark/config/proxy/your-proxy-name"

# 检查应用日志
tail -f logs/dwts-web-proxy.log | grep -i "InvisibleWatermarkProcessor"

# 验证配置参数
curl -X POST "http://localhost:9090/api/watermark/config/invisible/validate" \
  -H "Content-Type: application/json" \
  -d '{
    "encodingStrength": "medium",
    "embedDensity": 0.3
  }'
```

#### 问题2: 溯源分析失败
```bash
# 测试快速检测功能
curl -X POST "http://localhost:9090/api/watermark/trace/quick-detect" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "test content with invisible chars \u200B\u200C",
    "contentType": "text/plain"
  }'

# 检查数据库连接
mysql -u username -p -e "SELECT COUNT(*) FROM dwts_web_proxy_record;"
```

#### 问题3: 性能影响
```bash
# 调整为低性能影响配置
curl -X PUT "http://localhost:9090/api/watermark/config/invisible/1" \
  -H "Content-Type: application/json" \
  -d '{
    "enableInvisible": true,
    "encodingStrength": "low",
    "embedDensity": 0.2
  }'
```

### 2. 日志分析

```bash
# 查看水印处理相关日志
grep "watermark" logs/dwts-web-proxy.log | tail -50

# 查看错误日志
grep "ERROR" logs/dwts-web-proxy.log | grep -i watermark

# 查看性能日志
grep "处理耗时" logs/dwts-web-proxy.log | tail -20
```

### 3. 数据库维护

```sql
-- 查看水印记录统计
SELECT 
    watermark_type,
    COUNT(*) as count,
    AVG(process_time) as avg_process_time
FROM dwts_web_proxy_record 
WHERE watermark_added = TRUE 
GROUP BY watermark_type;

-- 清理旧记录（保留最近30天）
DELETE FROM dwts_web_proxy_record 
WHERE create_time < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 查看溯源记录
SELECT * FROM watermark_trace_record 
ORDER BY create_time DESC 
LIMIT 10;
```

## 📋 最佳实践

### 1. 安全配置建议

- **生产环境**: 使用 `medium` 或 `high` 编码强度
- **测试环境**: 使用 `low` 编码强度以减少性能影响
- **水印文本**: 包含时间戳和IP地址变量，如 `"DWTS-{IP}-{TIME}"`

### 2. 性能优化

- **嵌入密度**: 根据安全需求调整，建议范围 0.2-0.6
- **最大嵌入长度**: 根据内容大小调整，避免过大影响性能
- **定期清理**: 设置自动清理旧的访问记录

### 3. 监控告警

- **配置监控**: 定期检查暗水印配置状态
- **性能监控**: 监控水印处理耗时
- **异常告警**: 设置溯源分析异常告警

## 🔗 API 参考

完整的API文档可以通过以下地址访问：
- Swagger UI: http://localhost:9090/swagger-ui.html
- API Docs: http://localhost:9090/v2/api-docs

主要API端点：
- 配置管理: `/api/watermark/config/*`
- 溯源分析: `/api/watermark/trace/*`
- 健康检查: `/actuator/health`
