package com.wzsec.utils;

import cn.hutool.core.lang.Console;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.*;
import java.util.List;

/**
 * 写入文件工具类
 */
@Slf4j
public class WriteFileUtils {

    /**
     * 写入txt文件
     *
     * @param listData   列表数据
     * @param outputPath 输出路径
     * <AUTHOR>
     * @date 2019年11月12日
     */
    public static void writeTxtFile(List<String> listData, String outputPath) {
        try {
            String line = System.getProperty("line.separator");//平台换行!
            File file = new File(outputPath);
            //如果没有文件就创建
            if (!file.isFile()) {
                //获取父目录
                File fileParent = file.getParentFile();
                //判断是否存在
                if (!fileParent.exists()) {
                    //创建父目录文件
                    fileParent.mkdirs();
                }
                file.createNewFile();
            }
            OutputStreamWriter write = new OutputStreamWriter(new FileOutputStream(file), "UTF-8");
            //将数据写入文件中
            BufferedWriter writer = new BufferedWriter(write);
            for (String lineData : listData) {
                writer.write(lineData + line);
            }
            writer.close();
        } catch (FileNotFoundException e) {
            log.error("没有找到指定文件");
        } catch (IOException e) {
            log.error("文件读写出错");
        }

    }


    /**
     * 写入excel文件(xls)
     *
     * @param maskDataList 掩码数据列表
     * @param length       数据列数
     * @param outputPath   输出路径
     * @param outputName   输出名称
     * <AUTHOR>
     * @date 2020年2月12日
     */
    public static void writeExcelFile(List<String> maskDataList, int length, String outputPath,
                                      String outputName) {
        // 创建HSSFWorkbook，调用模板
        String[][] data = new String[maskDataList.size()][length];
        for (int i = 0; i < maskDataList.size(); i++) {
            data[i] = new String[length];
            String[] dataarr = maskDataList.get(i).split(",");
            for (int j = 0; j < dataarr.length; j++) {
                data[i][j] = dataarr[j];
            }
        }
        HSSFWorkbook wb = ExcelUtil.getHSSFWorkbook(outputName, data, null);
        OutputStream os = null;
        try {
            os = new FileOutputStream(new File(outputPath + File.separator + outputName));
            wb.write(os);
            wb.close();
        } catch (FileNotFoundException e) {
            log.error("没有找到指定文件");
        } catch (IOException e) {
            log.error("文件读写出错");
        } finally {
            try {
                wb.close();
                if (os != null) {
                    os.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }

        }
    }


    /**
     * 写入excel文件(xlsx)
     *
     * @param maskDataList 掩码数据列表
     * @param length       长
     * @param outputPath   输出路径
     * @param outputName   输出名称
     * @param splitstr     分隔符
     * <AUTHOR>
     * @date 2020年10月22日
     */
    public static void writeXSSFWorkbook(List<String> maskDataList, int length, String outputPath, String outputName, String splitstr) {
        // 创建HSSFWorkbook，调用模板
        String[][] data = new String[maskDataList.size()][length];
        for (int i = 0; i < maskDataList.size(); i++) {
            data[i] = new String[length];
            String[] dataarr = maskDataList.get(i).split(splitstr);
            for (int j = 0; j < dataarr.length; j++) {
                data[i][j] = dataarr[j];
            }
        }
        XSSFWorkbook wb = null;
        try {
            wb = ExcelUtil.getXSSFWorkbook(outputName, data, null);
        } catch (Exception e) {
            log.error(e.getMessage());
            Console.log("一次性写入数据长度为: {}", data.length);
        }
        OutputStream os = null;
        try {
            File file = new File(outputPath + File.separator + outputName);
            //如果没有文件就创建
            if (!file.isFile()) {
                //获取父目录
                File fileParent = file.getParentFile();
                //判断是否存在
                if (!fileParent.exists()) {
                    //创建父目录文件
                    fileParent.mkdirs();
                }
            }
            os = new FileOutputStream(file, true);
            wb.write(os);
        } catch (FileNotFoundException e) {
            log.error("没有找到指定文件");
        } catch (IOException e) {
            log.error("文件读写出错");
        } finally {
            try {
                wb.close();
                if (os != null) {
                    os.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

}
