/*
 Navicat Premium Data Transfer

 Source Server         : 【MySQL_*************_3306_root】
 Source Server Type    : MySQL
 Source Server Version : 80028 (8.0.28)
 Source Host           : *************:3306
 Source Schema         : dwts-clean

 Target Server Type    : MySQL
 Target Server Version : 80028 (8.0.28)
 File Encoding         : 65001

 Date: 05/08/2025 15:40:05
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for dwts_web_proxy_record
-- ----------------------------
DROP TABLE IF EXISTS `dwts_web_proxy_record`;
CREATE TABLE `dwts_web_proxy_record`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `proxy_config_id` bigint NULL DEFAULT NULL COMMENT '代理配置ID',
  `request_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '请求IP',
  `request_port` int NULL DEFAULT NULL COMMENT '请求端口',
  `request_path` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '请求路径',
  `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '请求方法',
  `request_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '请求类型 (PAGE/API/RESOURCE)',
  `response_status` int NULL DEFAULT NULL COMMENT '响应状态码',
  `response_content_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '响应内容类型',
  `response_size` bigint NULL DEFAULT NULL COMMENT '响应大小（字节）',
  `watermark_added` tinyint(1) NULL DEFAULT 0 COMMENT '是否添加了水印',
  `watermark_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '水印类型 (PAGE/JSON/XML/HEADER)',
  `watermark_content` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '水印内容',
  `process_time` bigint NULL DEFAULT NULL COMMENT '处理耗时（毫秒）',
  `user_agent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户代理',
  `referer` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '引用页面',
  `session_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '会话ID',
  `error_message` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '错误信息',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_proxy_config_id`(`proxy_config_id` ASC) USING BTREE,
  INDEX `idx_request_ip`(`request_ip` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE,
  INDEX `idx_request_type`(`request_type` ASC) USING BTREE,
  INDEX `idx_watermark_type`(`watermark_type` ASC) USING BTREE,
  INDEX `idx_record_time_range`(`create_time` ASC, `proxy_config_id` ASC) USING BTREE,
  INDEX `idx_record_ip_time`(`request_ip` ASC, `create_time` ASC) USING BTREE,
  INDEX `idx_record_watermark`(`watermark_added` ASC, `watermark_type` ASC) USING BTREE,
  CONSTRAINT `fk_proxy_record_config` FOREIGN KEY (`proxy_config_id`) REFERENCES `dwts_web_proxy_config` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'Web代理访问记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of dwts_web_proxy_record
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
