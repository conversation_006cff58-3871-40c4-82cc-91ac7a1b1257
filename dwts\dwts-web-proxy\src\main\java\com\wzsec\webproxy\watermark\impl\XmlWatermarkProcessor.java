package com.wzsec.webproxy.watermark.impl;

import com.wzsec.webproxy.domain.WebProxyConfig;
import com.wzsec.webproxy.watermark.AbstractWatermarkProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.w3c.dom.Comment;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import javax.servlet.http.HttpServletRequest;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.ByteArrayInputStream;
import java.io.StringWriter;

/**
 * XML接口水印处理器
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Slf4j
@Component
public class XmlWatermarkProcessor extends AbstractWatermarkProcessor {

    @Override
    public byte[] processWatermark(byte[] content, String contentType, 
                                 HttpServletRequest request, WebProxyConfig config) {
        try {
            if (!config.getEnableApiWatermark()) {
                return content;
            }

            String xmlString = safeToString(content);
            if (xmlString.trim().isEmpty()) {
                return content;
            }

            // 解析XML
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.parse(new ByteArrayInputStream(content));
            
            // 生成水印信息
            String watermarkText = generateWatermarkText(request, config);
            
            // 注入水印
            injectWatermarkToXml(doc, watermarkText, request, config);
            
            // 转换回XML字符串
            TransformerFactory transformerFactory = TransformerFactory.newInstance();
            Transformer transformer = transformerFactory.newTransformer();
            transformer.setOutputProperty("encoding", "UTF-8");
            transformer.setOutputProperty("indent", "yes");
            
            StringWriter writer = new StringWriter();
            transformer.transform(new DOMSource(doc), new StreamResult(writer));
            
            return safeToBytes(writer.toString());
            
        } catch (Exception e) {
            log.error("XML水印处理失败", e);
            return content; // 失败时返回原内容
        }
    }

    /**
     * 向XML中注入水印
     */
    private void injectWatermarkToXml(Document doc, String watermarkText, 
                                    HttpServletRequest request, WebProxyConfig config) {
        
        Element root = doc.getDocumentElement();
        if (root == null) {
            return;
        }
        
        // 方式1：添加水印属性到根元素
        root.setAttribute("dwts-watermark", watermarkText);
        root.setAttribute("dwts-timestamp", String.valueOf(System.currentTimeMillis()));
        root.setAttribute("dwts-ip", getClientIpAddress(request));
        root.setAttribute("dwts-user", getCurrentUser(request));
        
        // 方式2：添加水印注释
        Comment watermarkComment = doc.createComment(
            String.format(" DWTS Watermark: %s | IP: %s | Time: %d | User: %s ", 
                         watermarkText, 
                         getClientIpAddress(request),
                         System.currentTimeMillis(),
                         getCurrentUser(request))
        );
        root.insertBefore(watermarkComment, root.getFirstChild());
        
        // 方式3：添加水印元素
        Element watermarkElement = doc.createElement("dwts-watermark");
        
        // 添加水印子元素
        Element textElement = doc.createElement("text");
        textElement.setTextContent(watermarkText);
        watermarkElement.appendChild(textElement);
        
        Element timestampElement = doc.createElement("timestamp");
        timestampElement.setTextContent(String.valueOf(System.currentTimeMillis()));
        watermarkElement.appendChild(timestampElement);
        
        Element ipElement = doc.createElement("ip");
        ipElement.setTextContent(getClientIpAddress(request));
        watermarkElement.appendChild(ipElement);
        
        Element userElement = doc.createElement("user");
        userElement.setTextContent(getCurrentUser(request));
        watermarkElement.appendChild(userElement);
        
        Element pathElement = doc.createElement("path");
        pathElement.setTextContent(request.getRequestURI());
        watermarkElement.appendChild(pathElement);
        
        Element methodElement = doc.createElement("method");
        methodElement.setTextContent(request.getMethod());
        watermarkElement.appendChild(methodElement);
        
        if (config != null) {
            Element proxyElement = doc.createElement("proxy");
            proxyElement.setAttribute("name", config.getProxyName());
            proxyElement.setAttribute("port", String.valueOf(config.getProxyPort()));
            watermarkElement.appendChild(proxyElement);
        }
        
        // 将水印元素添加到根元素的末尾
        root.appendChild(watermarkElement);
    }

    @Override
    public boolean canHandle(String contentType) {
        return isContentTypeMatch(contentType, "application/xml", "text/xml", 
                                 "application/soap+xml", "application/xhtml+xml");
    }

    @Override
    public String getProcessorName() {
        return "XmlWatermarkProcessor";
    }

    @Override
    public String getWatermarkType() {
        return "XML";
    }
}
