package com.wzsec.webproxy.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 代理端口SSL配置属性
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Data
@Component
@ConfigurationProperties(prefix = "dwts.web-proxy.proxy-ports-ssl")
public class ProxyPortsSslProperties {

    /**
     * 是否为代理端口启用HTTPS
     */
    private boolean enabled = false;

    /**
     * SSL证书文件路径（支持classpath:前缀）
     */
    private String keystoreFile = "classpath:keystore.p12";

    /**
     * SSL证书密码
     */
    private String keystorePassword = "changeit";

    /**
     * SSL证书类型
     */
    private String keystoreType = "PKCS12";

    /**
     * SSL证书别名
     */
    private String keyAlias = "tomcat";

    /**
     * 检查SSL配置是否有效
     */
    public boolean isValidSslConfig() {
        return enabled && 
               keystoreFile != null && !keystoreFile.trim().isEmpty() &&
               keystorePassword != null && !keystorePassword.trim().isEmpty() &&
               keystoreType != null && !keystoreType.trim().isEmpty() &&
               keyAlias != null && !keyAlias.trim().isEmpty();
    }
}
